import sys
import os
from PyQt6.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, 
                            QHBoxLayout, QWidget, QPushButton, QTextEdit, 
                            QLabel, QFileDialog, QProgressBar, QMessageBox,
                            QComboBox, QCheckBox, QLineEdit, QGroupBox, QTabWidget)
from PyQt6.QtCore import QThread, pyqtSignal, Qt
from PyQt6.QtGui import QFont
import re


class FilterWorker(QThread):
    """后台工作线程，用于处理文件筛选"""
    progress_updated = pyqtSignal(int)
    status_updated = pyqtSignal(str)
    result_updated = pyqtSignal(str)
    finished_signal = pyqtSignal(bool, str)
    
    def __init__(self, input_files, output_file, search_pattern, case_sensitive=False):
        super().__init__()
        self.input_files = input_files if isinstance(input_files, list) else [input_files]
        self.output_file = output_file
        self.search_pattern = search_pattern
        self.case_sensitive = case_sensitive
        self.filtered_lines = []
        self.unmatched_lines = {}  # 存储每个文件的不匹配行
        
    def run(self):
        try:
            total_files = len(self.input_files)
            self.status_updated.emit(f"开始处理 {total_files} 个文件...")
            
            # 编译正则表达式
            flags = 0 if self.case_sensitive else re.IGNORECASE
            pattern = re.compile(self.search_pattern, flags)
            
            all_lines_count = 0
            processed_lines = 0
            
            # 首先统计所有文件的总行数
            for file_path in self.input_files:
                try:
                    with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                        lines = f.readlines()
                        all_lines_count += len(lines)
                except Exception as e:
                    self.status_updated.emit(f"读取文件 {file_path} 失败: {str(e)}")
                    continue
            
            self.status_updated.emit(f"总共需要处理 {all_lines_count} 行数据...")
            
            # 处理每个文件
            for file_index, file_path in enumerate(self.input_files):
                try:
                    self.status_updated.emit(f"正在处理文件 {file_index + 1}/{total_files}: {os.path.basename(file_path)}")
                    
                    # 读取文件
                    with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                        lines = f.readlines()
                    
                    file_unmatched = []
                    file_matched_count = 0
                    
                    # 筛选当前文件的行
                    for line in lines:
                        if pattern.search(line):
                            self.filtered_lines.append(line.strip())
                            file_matched_count += 1
                        else:
                            file_unmatched.append(line)
                        
                        processed_lines += 1
                        
                        # 更新总体进度
                        progress = int(processed_lines / all_lines_count * 100)
                        self.progress_updated.emit(progress)
                        
                        # 每1000行更新一次状态
                        if processed_lines % 1000 == 0:
                            self.status_updated.emit(f"已处理 {processed_lines}/{all_lines_count} 行，找到 {len(self.filtered_lines)} 条匹配记录")
                    
                    # 保存当前文件的不匹配行
                    self.unmatched_lines[file_path] = file_unmatched
                    
                    self.status_updated.emit(f"文件 {os.path.basename(file_path)} 处理完成，找到 {file_matched_count} 条匹配记录")
                    
                except Exception as e:
                    self.status_updated.emit(f"处理文件 {file_path} 时出错: {str(e)}")
                    continue
            
            # 保存所有匹配结果到输出文件
            self.status_updated.emit(f"正在保存 {len(self.filtered_lines)} 条记录到 {self.output_file}...")
            
            with open(self.output_file, 'w', encoding='utf-8') as f:
                for line in self.filtered_lines:
                    f.write(line + '\n')
            
            # 更新所有原文件，删除匹配的行
            self.status_updated.emit("正在从原文件删除匹配的行...")
            updated_files = 0
            for file_path, unmatched_lines in self.unmatched_lines.items():
                try:
                    with open(file_path, 'w', encoding='utf-8') as f:
                        f.writelines(unmatched_lines)
                    updated_files += 1
                except Exception as e:
                    self.status_updated.emit(f"更新文件 {file_path} 失败: {str(e)}")
            
            # 发送结果预览
            preview = '\n'.join(self.filtered_lines[:20])  # 显示前20行
            if len(self.filtered_lines) > 20:
                preview += f'\n\n... 还有 {len(self.filtered_lines) - 20} 行数据 ...'
            
            self.result_updated.emit(preview)
            self.finished_signal.emit(True, f"筛选完成！处理了 {total_files} 个文件，找到并移除了 {len(self.filtered_lines)} 条包含 '{self.search_pattern}' 的记录")
            
        except Exception as e:
            self.finished_signal.emit(False, f"处理过程中出现错误：{str(e)}")


class UniversalFilterApp(QMainWindow):
    def __init__(self):
        super().__init__()
        self.worker = None
        self.input_files = []  # 改为文件列表
        self.current_file = None  # Store the current file path for text editor
        self.init_ui()
        
    def init_ui(self):
        self.setWindowTitle("通用数据筛选与链接清理工具 - 支持多文件")
        self.setGeometry(100, 100, 1000, 800)
        
        # 创建中央widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        layout = QVBoxLayout()
        
        # 标题
        title = QLabel("通用数据筛选与链接清理工具")
        title.setFont(QFont("Arial", 18, QFont.Weight.Bold))
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title)
        
        subtitle = QLabel("支持多文件关键词筛选、链接清理、去重功能")
        subtitle.setFont(QFont("Arial", 12))
        subtitle.setAlignment(Qt.AlignmentFlag.AlignCenter)
        subtitle.setStyleSheet("color: #666; margin-bottom: 10px;")
        layout.addWidget(subtitle)
        
        # 创建选项卡
        tab_widget = QTabWidget()
        
        # 第一个选项卡：数据筛选
        filter_tab = self.create_filter_tab()
        tab_widget.addTab(filter_tab, "数据筛选")
        
        # 第二个选项卡：链接清理
        cleaner_tab = self.create_cleaner_tab()
        tab_widget.addTab(cleaner_tab, "链接清理")
        
        layout.addWidget(tab_widget)
        central_widget.setLayout(layout)
        
        # 初始化自动文件名
        self.on_auto_name_toggled(False)
    
    def create_filter_tab(self):
        """创建数据筛选选项卡"""
        filter_widget = QWidget()
        layout = QVBoxLayout()
        
        # 文件选择区域
        file_group = QGroupBox("文件选择")
        file_layout = QVBoxLayout()
        
        file_select_layout = QHBoxLayout()
        self.file_label = QLabel("请选择要筛选的文件:")
        self.file_button = QPushButton("选择单个文件")
        self.file_button.clicked.connect(self.select_input_file)
        self.multi_file_button = QPushButton("选择多个文件")
        self.multi_file_button.clicked.connect(self.select_multiple_files)
        self.clear_files_button = QPushButton("清空文件列表")
        self.clear_files_button.clicked.connect(self.clear_file_list)
        
        file_select_layout.addWidget(self.file_label)
        file_select_layout.addWidget(self.file_button)
        file_select_layout.addWidget(self.multi_file_button)
        file_select_layout.addWidget(self.clear_files_button)
        file_layout.addLayout(file_select_layout)
        
        # 显示已选文件列表
        self.selected_files_text = QTextEdit()
        self.selected_files_text.setMaximumHeight(100)
        self.selected_files_text.setPlaceholderText("已选择的文件将在这里显示...")
        self.selected_files_text.setReadOnly(True)
        file_layout.addWidget(QLabel("已选择的文件:"))
        file_layout.addWidget(self.selected_files_text)
        
        # 快速选择按钮
        quick_layout = QHBoxLayout()
        self.quick_tw_button = QPushButton("快速选择 tw.txt")
        self.quick_hk_button = QPushButton("快速选择 hk.txt")
        self.quick_us_button = QPushButton("快速选择 us.txt")
        
        self.quick_tw_button.clicked.connect(lambda: self.quick_select_file("tw.txt"))
        self.quick_hk_button.clicked.connect(lambda: self.quick_select_file("hk.txt"))
        self.quick_us_button.clicked.connect(lambda: self.quick_select_file("us.txt"))
        
        quick_layout.addWidget(self.quick_tw_button)
        quick_layout.addWidget(self.quick_hk_button)
        quick_layout.addWidget(self.quick_us_button)
        quick_layout.addStretch()
        file_layout.addLayout(quick_layout)
        
        file_group.setLayout(file_layout)
        layout.addWidget(file_group)
        
        # 筛选条件区域
        filter_group = QGroupBox("筛选条件")
        filter_layout = QVBoxLayout()
        
        # 关键词输入
        keyword_layout = QHBoxLayout()
        keyword_label = QLabel("搜索关键词:")
        self.keyword_input = QLineEdit()
        self.keyword_input.setPlaceholderText("输入要搜索的关键词，如：pccu.edu.tw、gmail.com、@163.com等")
        keyword_layout.addWidget(keyword_label)
        keyword_layout.addWidget(self.keyword_input)
        filter_layout.addLayout(keyword_layout)
        
        # 搜索模式选择
        mode_layout = QHBoxLayout()
        mode_label = QLabel("搜索模式:")
        self.pattern_combo = QComboBox()
        self.pattern_combo.addItems([
            "包含关键词 (默认)",
            "精确匹配",
            "开头匹配", 
            "结尾匹配",
            "正则表达式"
        ])
        self.pattern_combo.currentTextChanged.connect(self.on_pattern_changed)
        
        # 选项
        self.case_sensitive_cb = QCheckBox("区分大小写")
        self.whole_word_cb = QCheckBox("完整单词匹配")
        
        mode_layout.addWidget(mode_label)
        mode_layout.addWidget(self.pattern_combo)
        mode_layout.addWidget(self.case_sensitive_cb)
        mode_layout.addWidget(self.whole_word_cb)
        mode_layout.addStretch()
        filter_layout.addLayout(mode_layout)
        
        # 输出文件设置
        output_layout = QHBoxLayout()
        output_label = QLabel("输出文件名:")
        self.output_input = QLineEdit()
        self.output_input.setPlaceholderText("输入保存结果的文件名")
        self.auto_name_cb = QCheckBox("自动生成文件名")
        self.auto_name_cb.setChecked(False)
        self.auto_name_cb.toggled.connect(self.on_auto_name_toggled)
        
        output_layout.addWidget(output_label)
        output_layout.addWidget(self.output_input)
        output_layout.addWidget(self.auto_name_cb)
        filter_layout.addLayout(output_layout)
        
        filter_group.setLayout(filter_layout)
        layout.addWidget(filter_group)
        
        # 预设关键词快捷按钮
        preset_group = QGroupBox("常用关键词预设")
        preset_layout = QHBoxLayout()
        
        preset_buttons = [
            ("pccu.edu.tw", "pccu.edu.tw"),
            ("Gmail", "gmail.com"),
            ("163邮箱", "@163.com"),
            ("QQ邮箱", "@qq.com"),
            ("学校邮箱", ".edu"),
            ("政府网站", ".gov")
        ]
        
        for text, keyword in preset_buttons:
            btn = QPushButton(text)
            btn.clicked.connect(lambda checked, k=keyword: self.set_keyword(k))
            preset_layout.addWidget(btn)
        
        preset_layout.addStretch()
        preset_group.setLayout(preset_layout)
        layout.addWidget(preset_group)
        
        # 控制按钮
        button_layout = QHBoxLayout()
        self.start_button = QPushButton("开始筛选")
        self.start_button.clicked.connect(self.start_filtering)
        self.start_button.setEnabled(False)
        
        self.stop_button = QPushButton("停止")
        self.stop_button.clicked.connect(self.stop_filtering)
        self.stop_button.setEnabled(False)
        
        self.clear_button = QPushButton("清空结果")
        self.clear_button.clicked.connect(self.clear_results)
        
        button_layout.addWidget(self.start_button)
        button_layout.addWidget(self.stop_button)
        button_layout.addWidget(self.clear_button)
        button_layout.addStretch()
        layout.addLayout(button_layout)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        # 状态标签
        self.status_label = QLabel("就绪")
        layout.addWidget(self.status_label)
        
        # 结果显示区域
        result_label = QLabel("筛选结果预览:")
        layout.addWidget(result_label)
        
        self.result_text = QTextEdit()
        self.result_text.setMaximumHeight(300)
        self.result_text.setPlaceholderText("筛选结果将在这里显示...")
        layout.addWidget(self.result_text)
        
        filter_widget.setLayout(layout)
        return filter_widget
    
    def create_cleaner_tab(self):
        """创建链接清理选项卡"""
        cleaner_widget = QWidget()
        layout = QVBoxLayout()
        
        # 文件操作区域
        file_group = QGroupBox("文件操作")
        file_layout = QVBoxLayout()
        
        # 文件加载按钮
        load_layout = QHBoxLayout()
        load_button = QPushButton('打开文件')
        load_button.clicked.connect(self.load_file_for_cleaning)
        self.current_file_label = QLabel("未选择文件")
        load_layout.addWidget(load_button)
        load_layout.addWidget(self.current_file_label)
        load_layout.addStretch()
        file_layout.addLayout(load_layout)
        
        file_group.setLayout(file_layout)
        layout.addWidget(file_group)
        
        # 文本编辑区域
        edit_group = QGroupBox("文本编辑")
        edit_layout = QVBoxLayout()
        
        # 创建文本编辑器
        self.text_edit = QTextEdit()
        self.text_edit.setPlaceholderText("请先打开文件或在此输入要处理的文本...")
        edit_layout.addWidget(self.text_edit)
        
        edit_group.setLayout(edit_layout)
        layout.addWidget(edit_group)
        
        # 处理功能区域
        process_group = QGroupBox("处理功能")
        process_layout = QVBoxLayout()
        
        # 第一行按钮
        button_layout1 = QHBoxLayout()
        clean_button = QPushButton('清理链接')
        clean_button.clicked.connect(self.clean_urls)
        clean_button.setToolTip("移除每行冒号前的内容")
        
        dedup_button = QPushButton('去除重复')
        dedup_button.clicked.connect(self.remove_duplicates)
        dedup_button.setToolTip("删除重复的行（不区分大小写）")
        
        button_layout1.addWidget(clean_button)
        button_layout1.addWidget(dedup_button)
        button_layout1.addStretch()
        process_layout.addLayout(button_layout1)
        
        # 第二行按钮
        button_layout2 = QHBoxLayout()
        save_button = QPushButton('另存为')
        save_button.clicked.connect(self.save_file)
        save_button.setToolTip("将当前内容保存为新文件")
        
        clear_text_button = QPushButton('清空文本')
        clear_text_button.clicked.connect(self.clear_text)
        clear_text_button.setToolTip("清空文本编辑器内容")
        
        button_layout2.addWidget(save_button)
        button_layout2.addWidget(clear_text_button)
        button_layout2.addStretch()
        process_layout.addLayout(button_layout2)
        
        process_group.setLayout(process_layout)
        layout.addWidget(process_group)
        
        # 状态信息
        self.cleaner_status_label = QLabel("就绪")
        layout.addWidget(self.cleaner_status_label)
        
        cleaner_widget.setLayout(layout)
        return cleaner_widget

    def on_pattern_changed(self):
        """当搜索模式改变时的处理"""
        mode = self.pattern_combo.currentText()
        if mode == "正则表达式":
            self.keyword_input.setPlaceholderText("输入正则表达式，如：.*\\.edu\\.tw、^admin@、\\d{4}-\\d{2}-\\d{2}等")
        else:
            self.keyword_input.setPlaceholderText("输入要搜索的关键词，如：pccu.edu.tw、gmail.com、@163.com等")
    
    def on_auto_name_toggled(self, checked):
        """自动文件名切换处理"""
        self.output_input.setEnabled(not checked)
        if checked:
            keyword = self.keyword_input.text().strip()
            if keyword:
                # 提取域名主要部分生成文件名
                filename = self.extract_domain_name(keyword)
                self.output_input.setText(filename)
    
    def extract_domain_name(self, keyword):
        """从关键词中提取域名主要部分生成文件名"""
        # 移除可能的前缀符号（如@、.等）
        clean_keyword = keyword.lstrip('@.')
        
        # 如果包含域名格式（包含点），提取主域名
        if '.' in clean_keyword:
            # 分割域名
            parts = clean_keyword.split('.')
            # 取第一个部分作为文件名
            domain_name = parts[0]
            return f"{domain_name}.txt"
        else:
            # 如果不是域名格式，使用原有逻辑
            safe_keyword = re.sub(r'[^\w\-_.]', '_', clean_keyword)
            return f"filtered_{safe_keyword}.txt"
    
    def set_keyword(self, keyword):
        """设置关键词"""
        self.keyword_input.setText(keyword)
        if self.auto_name_cb.isChecked():
            # 自动生成对应的文件名
            filename = self.extract_domain_name(keyword)
            self.output_input.setText(filename)
    
    def select_input_file(self):
        """选择单个文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, 
            "选择要筛选的文件", 
            "", 
            "文本文件 (*.txt);;所有文件 (*)"
        )
        
        if file_path:
            if file_path not in self.input_files:  # 避免重复添加
                self.input_files.append(file_path)
                self.update_file_display()
                self.start_button.setEnabled(True)
            else:
                QMessageBox.information(self, "提示", f"文件 {os.path.basename(file_path)} 已经在列表中了！")
    
    def select_multiple_files(self):
        """选择多个文件"""
        file_paths, _ = QFileDialog.getOpenFileNames(
            self,
            "选择要筛选的文件（可多选）",
            "",
            "文本文件 (*.txt);;所有文件 (*)"
        )
        
        if file_paths:
            new_files = []
            for file_path in file_paths:
                if file_path not in self.input_files:  # 避免重复添加
                    self.input_files.append(file_path)
                    new_files.append(os.path.basename(file_path))
            
            if new_files:
                self.update_file_display()
                self.start_button.setEnabled(True)
                QMessageBox.information(self, "成功", f"添加了 {len(new_files)} 个新文件")
            else:
                QMessageBox.information(self, "提示", "所选文件都已经在列表中了！")
    
    def clear_file_list(self):
        """清空文件列表"""
        if self.input_files:
            reply = QMessageBox.question(
                self, 
                "确认", 
                f"确定要清空已选择的 {len(self.input_files)} 个文件吗？",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )
            if reply == QMessageBox.StandardButton.Yes:
                self.input_files.clear()
                self.update_file_display()
                self.start_button.setEnabled(False)
    
    def update_file_display(self):
        """更新文件显示"""
        if self.input_files:
            file_list = []
            for i, file_path in enumerate(self.input_files, 1):
                filename = os.path.basename(file_path)
                file_list.append(f"{i}. {filename}")
            
            self.selected_files_text.setPlainText('\n'.join(file_list))
            self.file_label.setText(f"已选择 {len(self.input_files)} 个文件:")
        else:
            self.selected_files_text.clear()
            self.file_label.setText("请选择要筛选的文件:")
            
    def quick_select_file(self, filename):
        """快速选择指定文件"""
        if os.path.exists(filename):
            if filename not in self.input_files:  # 避免重复添加
                self.input_files.append(filename)
                self.update_file_display()
                self.start_button.setEnabled(True)
            else:
                QMessageBox.information(self, "提示", f"文件 {filename} 已经在列表中了！")
        else:
            QMessageBox.warning(self, "文件不存在", f"文件 {filename} 不存在！")
    
    def clear_results(self):
        """清空结果显示"""
        reply = QMessageBox.question(
            self,
            "清空选项",
            "要清空什么内容？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No | QMessageBox.StandardButton.Cancel
        )
        
        # 自定义按钮文本
        reply_box = QMessageBox(self)
        reply_box.setWindowTitle("清空选项")
        reply_box.setText("请选择要清空的内容：")
        
        clear_results_btn = reply_box.addButton("仅清空结果", QMessageBox.ButtonRole.YesRole)
        clear_all_btn = reply_box.addButton("清空结果和文件列表", QMessageBox.ButtonRole.NoRole)
        cancel_btn = reply_box.addButton("取消", QMessageBox.ButtonRole.RejectRole)
        
        reply_box.exec()
        clicked_button = reply_box.clickedButton()
        
        if clicked_button == clear_results_btn:
            # 仅清空结果
            self.result_text.clear()
            self.status_label.setText("就绪")
        elif clicked_button == clear_all_btn:
            # 清空结果和文件列表
            self.result_text.clear()
            self.status_label.setText("就绪")
            self.input_files.clear()
            self.update_file_display()
            self.start_button.setEnabled(False)
    
    # 链接清理相关功能
    def load_file_for_cleaning(self):
        """为链接清理功能加载文件"""
        file_name, _ = QFileDialog.getOpenFileName(self, '打开文件', '', '文本文件 (*.txt);;所有文件 (*)')
        if file_name:
            try:
                with open(file_name, 'r', encoding='utf-8') as file:
                    content = file.read()
                    self.text_edit.setText(content)
                    self.current_file = file_name  # Store the file path
                    filename = os.path.basename(file_name)
                    self.current_file_label.setText(f"已打开: {filename}")
                    self.cleaner_status_label.setText(f"已加载文件: {filename}")
            except Exception as e:
                QMessageBox.critical(self, '错误', f'加载文件时出错：{str(e)}')
    
    def clean_urls(self):
        """清理链接 - 移除冒号前的内容"""
        content = self.text_edit.toPlainText()
        if not content.strip():
            QMessageBox.warning(self, '警告', '文本编辑器为空，请先加载文件或输入文本！')
            return
            
        cleaned_lines = []
        
        for line in content.split('\n'):
            if ':' in line:
                # Find the first colon and remove everything before it
                parts = line.split(':', 1)
                if len(parts) > 1:
                    cleaned_lines.append(parts[1])
            else:
                cleaned_lines.append(line)
        
        cleaned_content = '\n'.join(cleaned_lines)
        self.text_edit.setText(cleaned_content)
        
        # Auto save to the original file if one is loaded
        if self.current_file:
            try:
                with open(self.current_file, 'w', encoding='utf-8') as file:
                    file.write(cleaned_content)
                QMessageBox.information(self, '成功', '链接已清理并自动保存到原文件！')
                self.cleaner_status_label.setText("链接清理完成并已保存")
            except Exception as e:
                QMessageBox.critical(self, '错误', f'保存文件时出错：{str(e)}')
        else:
            QMessageBox.information(self, '成功', '链接已清理！请保存文件。')
            self.cleaner_status_label.setText("链接清理完成，请保存文件")
    
    def remove_duplicates(self):
        """去除重复行"""
        content = self.text_edit.toPlainText()
        if not content.strip():
            QMessageBox.warning(self, '警告', '文本编辑器为空，请先加载文件或输入文本！')
            return
            
        lines = content.split('\n')
        unique_lines = []
        duplicates_count = 0
        
        for i, line in enumerate(lines):
            is_duplicate = False
            if line.strip():  # Only process non-empty lines
                # Compare current line with all previous lines
                current_line = line.strip()  # Remove leading/trailing whitespace
                for existing_line in unique_lines:
                    if current_line.lower() == existing_line.lower():  # Case-insensitive comparison
                        is_duplicate = True
                        duplicates_count += 1
                        break
                
                if not is_duplicate:
                    unique_lines.append(current_line)
        
        # Join the unique lines back together
        deduped_content = '\n'.join(unique_lines)
        self.text_edit.setText(deduped_content)
        
        # Auto save to the original file if one is loaded
        if self.current_file:
            try:
                with open(self.current_file, 'w', encoding='utf-8') as file:
                    file.write(deduped_content)
                QMessageBox.information(self, '成功', f'已删除{duplicates_count}个重复内容并自动保存到原文件！')
                self.cleaner_status_label.setText(f"去重完成，删除了{duplicates_count}个重复项")
            except Exception as e:
                QMessageBox.critical(self, '错误', f'保存文件时出错：{str(e)}')
        else:
            QMessageBox.information(self, '成功', f'已删除{duplicates_count}个重复内容！请保存文件。')
            self.cleaner_status_label.setText(f"去重完成，删除了{duplicates_count}个重复项，请保存文件")
    
    def save_file(self):
        """另存为功能"""
        content = self.text_edit.toPlainText()
        if not content.strip():
            QMessageBox.warning(self, '警告', '文本编辑器为空，没有内容可保存！')
            return
            
        file_name, _ = QFileDialog.getSaveFileName(self, '保存文件', '', '文本文件 (*.txt);;所有文件 (*)')
        if file_name:
            try:
                with open(file_name, 'w', encoding='utf-8') as file:
                    file.write(content)
                QMessageBox.information(self, '成功', '文件保存成功！')
                filename = os.path.basename(file_name)
                self.cleaner_status_label.setText(f"文件已保存为: {filename}")
            except Exception as e:
                QMessageBox.critical(self, '错误', f'保存文件时出错：{str(e)}')
    
    def clear_text(self):
        """清空文本编辑器"""
        self.text_edit.clear()
        self.current_file = None
        self.current_file_label.setText("未选择文件")
        self.cleaner_status_label.setText("已清空文本编辑器")
            
    def start_filtering(self):
        if not self.input_files:
            QMessageBox.warning(self, "警告", "请先选择输入文件！")
            return
        
        keyword = self.keyword_input.text().strip()
        if not keyword:
            QMessageBox.warning(self, "警告", "请输入搜索关键词！")
            return
            
        # 根据搜索模式构建正则表达式
        mode = self.pattern_combo.currentText()
        
        if mode == "正则表达式":
            search_pattern = keyword
        elif mode == "精确匹配":
            if self.whole_word_cb.isChecked():
                search_pattern = r'\b' + re.escape(keyword) + r'\b'
            else:
                search_pattern = re.escape(keyword)
        elif mode == "开头匹配":
            search_pattern = r'^.*' + re.escape(keyword)
        elif mode == "结尾匹配":
            search_pattern = re.escape(keyword) + r'.*$'
        else:  # 包含关键词（默认）
            if self.whole_word_cb.isChecked():
                search_pattern = r'\b' + re.escape(keyword) + r'\b'
            else:
                search_pattern = re.escape(keyword)
            
        case_sensitive = self.case_sensitive_cb.isChecked()
        output_file = self.output_input.text().strip()
        
        if not output_file:
            output_file = "filtered_results.txt"
        
        # 重置UI
        self.result_text.clear()
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        self.start_button.setEnabled(False)
        self.stop_button.setEnabled(True)
        
        # 创建并启动工作线程
        self.worker = FilterWorker(self.input_files, output_file, search_pattern, case_sensitive)
        self.worker.progress_updated.connect(self.progress_bar.setValue)
        self.worker.status_updated.connect(self.status_label.setText)
        self.worker.result_updated.connect(self.result_text.setPlainText)
        self.worker.finished_signal.connect(self.filtering_finished)
        self.worker.start()
        
    def stop_filtering(self):
        if self.worker and self.worker.isRunning():
            self.worker.terminate()
            self.worker.wait()
            self.filtering_finished(False, "操作已取消")
            
    def filtering_finished(self, success, message):
        self.progress_bar.setVisible(False)
        self.start_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        
        if success:
            QMessageBox.information(self, "完成", message)
            output_file = self.output_input.text().strip()
            self.status_label.setText(f"筛选完成！结果已保存到 {output_file}")
        else:
            QMessageBox.warning(self, "错误", message)
            self.status_label.setText("操作失败")


def main():
    app = QApplication(sys.argv)
    
    # 设置应用程序属性
    app.setApplicationName("通用数据筛选与链接清理工具")
    app.setApplicationVersion("3.0")
    
    window = UniversalFilterApp()
    window.show()
    
    sys.exit(app.exec())


if __name__ == "__main__":
    main() 