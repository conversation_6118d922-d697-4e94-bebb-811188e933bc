# 请输入源文件
source_file = input("请输入源文件名: ")

# 请输入关键字
keyword = input("请输入关键字: ")

# 请输入保存到的文件
output_file = input("请输入保存到的文件名: ")

# 读取源文件内容
with open(source_file, 'r', encoding='utf-8') as infile:
    lines = infile.readlines()

# 写入包含关键字的行到输出文件
with open(output_file, 'w', encoding='utf-8') as nusfile:
    remaining_lines = []
    for line in lines:
        if keyword in line:
            nusfile.write(line)
        else:
            remaining_lines.append(line)

# 更新源文件，删除已写入输出文件的行
with open(source_file, 'w', encoding='utf-8') as outfile:
    outfile.writelines(remaining_lines)
