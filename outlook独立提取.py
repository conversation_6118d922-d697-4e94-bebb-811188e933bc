import os

# 定义关键字与对应的目标文件名
keywords_files = {
    # '.onmicrosoft.com': 'admin.txt',
    # '.edu:': 'outlook_us.txt',
    # 'edu.tw': 'tw.txt',
    # 'cas.columbia.edu/': 'columbia.txt',
    '.edu|': 'usa.txt',
}

# 构建源文件名
source_filenames = [
    'edu.txt',
]

# 遍历每个源文件名
for source_filename in source_filenames:
    # 如果文件存在，才进行处理
    if os.path.exists(source_filename):
        # 打开源文件进行读取
        with open(source_filename, 'r', encoding='utf-8') as source_file:
            # 创建一个字典来存储打开的文件对象
            file_handles = {}

            # 逐行读取源文件
            for line in source_file:
                # 检查每个关键字是否在当前行中
                for keyword, filename in keywords_files.items():
                    if keyword in line:
                        # 如果关键字在当前行中，将该行写入对应的目标文件
                        if filename not in file_handles:
                            file_handles[filename] = open(filename, 'a', encoding='utf-8')  # 使用'a'模式追加写入
                        file_handles[filename].write(line)

            # 关闭所有打开的文件
            for file in file_handles.values():
                file.close()
    else:
        print(f"{source_filename} 文件不存在。")
