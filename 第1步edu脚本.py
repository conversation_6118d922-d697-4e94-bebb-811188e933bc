import sys
import os
import concurrent.futures
import logging
import threading
import queue
import gc
from collections import defaultdict
from PyQt6.QtWidgets import (QApplication, QMainWindow, QGridLayout, QWidget,
                             QPushButton, QFileDialog, QTextEdit, QLabel,
                             QVBoxLayout, QHBoxLayout, QTableWidget, QTableWidgetItem,
                             QHeaderView, QProgressBar, QFrame, QSizePolicy)
from PyQt6.QtCore import QThread, pyqtSignal, Qt, QTimer
from PyQt6.QtGui import QFont

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 性能优化配置
CHUNK_SIZE = 50000  # 增大块大小以减少I/O操作
LARGE_FILE_THRESHOLD = 50 * 1024 * 1024  # 50MB阈值
MAX_WORKERS = min(8, os.cpu_count() or 1)  # 限制最大线程数
MEMORY_LIMIT = 500 * 1024 * 1024  # 500MB内存限制

class OptimizedFileProcessor:
    """优化的文件处理器，支持大文件和批量处理"""

    def __init__(self, progress_callback=None, log_callback=None):
        self.progress_callback = progress_callback
        self.log_callback = log_callback
        self.processed_data = defaultdict(list)

    def log(self, message):
        if self.log_callback:
            self.log_callback(message)

    def update_progress(self, value):
        if self.progress_callback:
            self.progress_callback(value)

    def read_file_chunked(self, file_path, chunk_size=CHUNK_SIZE):
        """分块读取文件，减少内存占用"""
        try:
            file_size = os.path.getsize(file_path)
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                while True:
                    chunk = f.readlines(chunk_size)
                    if not chunk:
                        break
                    yield chunk
        except Exception as e:
            self.log(f"读取文件 {file_path} 出错: {e}")
            yield []

    def process_files_parallel(self, file_paths, process_func, max_workers=MAX_WORKERS):
        """并行处理多个文件"""
        results = []
        total_files = len(file_paths)

        # 根据文件大小分组：小文件并行处理，大文件串行处理
        small_files = []
        large_files = []

        for file_path in file_paths:
            try:
                file_size = os.path.getsize(file_path)
                if file_size > LARGE_FILE_THRESHOLD:
                    large_files.append(file_path)
                else:
                    small_files.append(file_path)
            except:
                small_files.append(file_path)  # 默认当作小文件处理

        self.log(f"文件分组: {len(small_files)} 个小文件, {len(large_files)} 个大文件")

        processed_count = 0

        # 先并行处理小文件
        if small_files:
            with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
                future_to_file = {executor.submit(process_func, file_path): file_path
                                for file_path in small_files}

                for future in concurrent.futures.as_completed(future_to_file):
                    file_path = future_to_file[future]
                    try:
                        result = future.result()
                        results.append(result)
                        processed_count += 1
                        progress = int((processed_count / total_files) * 80)  # 80%用于文件处理
                        self.update_progress(progress)
                    except Exception as e:
                        self.log(f"处理文件 {file_path} 失败: {e}")

        # 串行处理大文件
        for file_path in large_files:
            try:
                result = process_func(file_path)
                results.append(result)
                processed_count += 1
                progress = int((processed_count / total_files) * 80)
                self.update_progress(progress)
            except Exception as e:
                self.log(f"处理大文件 {file_path} 失败: {e}")

        return results

    def write_results_optimized(self, results_dict, base_progress=80):
        """优化的结果写入，批量写入减少I/O"""
        total_files = len(results_dict)
        written_count = 0

        for filename, lines in results_dict.items():
            try:
                if lines:
                    # 批量写入，减少I/O操作
                    with open(filename, 'w', encoding='utf-8', buffering=8192) as f:
                        f.writelines(lines)
                    self.log(f"写入 {filename}: {len(lines)} 行")
                else:
                    # 创建空文件
                    with open(filename, 'w', encoding='utf-8') as f:
                        pass
                    self.log(f"创建空文件: {filename}")

                written_count += 1
                progress = base_progress + int((written_count / total_files) * (100 - base_progress))
                self.update_progress(progress)

            except Exception as e:
                self.log(f"写入文件 {filename} 失败: {e}")

        # 强制垃圾回收
        gc.collect()

class WorkerThread(QThread):
    """工作线程，用于执行耗时操作"""
    update_signal = pyqtSignal(str)
    progress_signal = pyqtSignal(int)  # 新增进度信号
    finished_signal = pyqtSignal()

    def __init__(self, function, *args, **kwargs):
        super().__init__()
        self.function = function
        self.args = args
        self.kwargs = kwargs

    def run(self):
        try:
            result = self.function(*self.args, **self.kwargs)
            self.finished_signal.emit()
        except Exception as e:
            self.update_signal.emit(f"错误: {str(e)}")
            logger.error(f"执行过程中出现错误: {e}")

class EduDataProcessor(QMainWindow):
    def __init__(self):
        super().__init__()
        self.initUI()

    def initUI(self):
        self.setWindowTitle('EDU DATA PROCESSOR')
        self.setGeometry(100, 100, 900, 700)

        # 创建中央部件和布局
        
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        
        # 创建标题标签
        title_label = QLabel('EDU DATA PROCESSOR')
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        main_layout.addWidget(title_label)
        
        # 创建按钮区域容器
        button_container = QFrame()
        
        # 创建按钮网格布局
        button_layout = QGridLayout(button_container)
        
        # 创建六个处理按钮
        self.button1 = QPushButton('国家提取')
        self.button2 = QPushButton('删除apply等')
        self.button3 = QPushButton('后缀提取')
        self.button4 = QPushButton('冒号替换')
        self.button5 = QPushButton('删除链接')
        self.button6 = QPushButton('去重处理')
        
        # 添加按钮到网格布局
        button_layout.addWidget(self.button1, 0, 0)
        button_layout.addWidget(self.button2, 0, 1)
        button_layout.addWidget(self.button3, 0, 2)
        button_layout.addWidget(self.button4, 1, 0)
        button_layout.addWidget(self.button5, 1, 1)
        button_layout.addWidget(self.button6, 1, 2)
        
        main_layout.addWidget(button_container)
        
        # 文件选择容器
        file_container = QFrame()
        
        # 文件选择布局
        file_layout = QVBoxLayout(file_container)
        
        # 顶部文件选择行
        file_header_layout = QHBoxLayout()
        self.file_label = QLabel('选择输入文件:')
        self.file_count_label = QLabel('未选择文件')
        self.file_select_button = QPushButton('浏览多个文件...')
        self.file_select_button.clicked.connect(self.select_file)
        
        file_header_layout.addWidget(self.file_label)
        file_header_layout.addWidget(self.file_count_label, 1)
        file_header_layout.addWidget(self.file_select_button)
        
        file_layout.addLayout(file_header_layout)
        
        # 添加文件列表显示区域
        self.file_list_area = QTextEdit()
        self.file_list_area.setReadOnly(True)
        file_layout.addWidget(self.file_list_area)
        
        main_layout.addWidget(file_container)
        
        # 进度条容器
        progress_container = QFrame()
        
        # 创建进度条布局
        progress_layout = QVBoxLayout(progress_container)
        
        # 创建进度标签
        self.progress_label = QLabel('处理进度:')
        progress_layout.addWidget(self.progress_label)
        
        # 创建进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setMinimum(0)
        self.progress_bar.setMaximum(100)
        self.progress_bar.setValue(0)
        self.progress_bar.setTextVisible(True)
        self.progress_bar.setFormat("%p%")
        progress_layout.addWidget(self.progress_bar)
        
        main_layout.addWidget(progress_container)
        
        # 日志容器
        log_container = QFrame()
        
        # 创建日志布局
        log_layout = QVBoxLayout(log_container)
        
        # 创建日志标签
        log_label = QLabel('处理日志:')
        log_layout.addWidget(log_label)
        
        # 创建日志显示区域
        self.log_area = QTextEdit()
        self.log_area.setReadOnly(True)
        log_layout.addWidget(self.log_area)
        
        main_layout.addWidget(log_container, 1)  # 日志区域应该占据剩余空间
        
        # 连接按钮到处理函数
        self.button1.clicked.connect(self.process_country_extraction)
        self.button2.clicked.connect(self.process_remove_apply)
        self.button3.clicked.connect(self.process_suffix_extraction)
        self.button4.clicked.connect(self.process_colon_replacement)
        self.button5.clicked.connect(self.process_remove_links)
        self.button6.clicked.connect(self.process_remove_duplicates)
        
        # 存储输入文件路径
        self.input_file_path = None
        # 存储多个输入文件路径
        self.input_file_paths = []

    def update_progress(self, value):
        """更新进度条的值"""
        self.progress_bar.setValue(value)
        if value == 100:
            self.progress_label.setText("处理完成!")
        else:
            self.progress_label.setText(f"处理进度: {value}%")

    def log_message(self, message):
        """向日志区域添加消息"""
        self.log_area.append(message)
    
    def select_file(self):
        """选择输入文件"""
        file_paths, _ = QFileDialog.getOpenFileNames(self, '选择输入文件', '', 'Text Files (*.txt)')
        if file_paths:
            self.input_file_paths = file_paths
            # 保留单文件路径用于兼容性
            self.input_file_path = file_paths[0] if file_paths else None
            
            # 更新文件计数标签
            if len(file_paths) == 1:
                self.file_count_label.setText(f"已选择 1 个文件")
            else:
                self.file_count_label.setText(f"已选择 {len(file_paths)} 个文件")
            
            # 更新文件列表显示
            self.file_list_area.clear()
            for path in file_paths:
                self.file_list_area.append(os.path.basename(path))
            
            self.log_message(f"已选择 {len(file_paths)} 个文件")
    
    # 添加一个帮助方法来确保文件夹存在
    def ensure_file_exists(self, file_path, default_content=""):
        """确保文件存在，如果不存在则创建它"""
        directory = os.path.dirname(file_path)
        if directory and not os.path.exists(directory):
            os.makedirs(directory)
            
        if not os.path.exists(file_path):
            with open(file_path, 'w', encoding='utf-8') as f:
                if default_content:
                    f.write(default_content)
            return False
        return True
    
    def get_formatted_filename(self, domain):
        """获取格式化的文件名，基于域名"""
        parts = domain.replace('.', ':').split(':')
        if len(parts) >= 2:
            return f"{parts[0]}.txt"  # 默认使用第一部分
        return f"{domain.split('.')[0]}.txt"

    # 第1部分: 国家提取 (优化版)
    def process_country_extraction(self):
        if not self.input_file_paths:
            self.log_message("错误: 请先选择输入文件")
            return

        self.log_message("开始国家提取处理...")
        self.progress_bar.setValue(0)
        self.progress_label.setText("处理进度: 0%")

        def extraction_task(self=self):
            # 使用优化的文件处理器
            processor = OptimizedFileProcessor(
                progress_callback=lambda x: self.extraction_thread.progress_signal.emit(x),
                log_callback=lambda x: self.extraction_thread.update_signal.emit(x)
            )

            # 预定义域名模式，提高匹配效率
            domain_patterns = {
                '.edu.tw/': 'tw.txt',
                '.edu.hk/': 'hk.txt',
                '.edu/': 'us.txt',
                '.ac.uk/': 'uk.txt'
            }

            # 初始化结果容器
            results = defaultdict(list)
            processed_files = 0
            failed_files = 0

            def process_single_file(file_path):
                """处理单个文件的函数"""
                file_results = defaultdict(list)
                processed_lines = 0

                try:
                    file_size = os.path.getsize(file_path)
                    processor.log(f"处理文件: {os.path.basename(file_path)} ({file_size/1024/1024:.1f}MB)")

                    # 使用优化的分块读取
                    for chunk in processor.read_file_chunked(file_path, CHUNK_SIZE):
                        for line in chunk:
                            # 使用更高效的字符串匹配
                            for pattern, output_file in domain_patterns.items():
                                if pattern in line:
                                    file_results[output_file].append(line)
                                    processed_lines += 1
                                    break  # 找到匹配后立即跳出循环

                    return file_results, processed_lines, None
                except Exception as e:
                    return defaultdict(list), 0, str(e)

            try:
                total_files = len(self.input_file_paths)
                processor.log(f"开始处理 {total_files} 个文件")

                # 使用并行处理
                file_results_list = processor.process_files_parallel(
                    self.input_file_paths,
                    process_single_file,
                    max_workers=min(4, MAX_WORKERS)  # 限制并发数避免内存过载
                )

                # 合并所有结果
                total_processed_lines = 0
                for file_result, processed_lines, error in file_results_list:
                    if error:
                        processor.log(f"处理文件出错: {error}")
                        failed_files += 1
                    else:
                        processed_files += 1
                        total_processed_lines += processed_lines

                        # 合并结果
                        for output_file, lines in file_result.items():
                            results[output_file].extend(lines)

                # 使用优化的结果写入
                processor.update_progress(80)
                processor.log("开始写入结果文件...")

                # 写入结果文件
                processor.write_results_optimized(results, base_progress=80)

                # 统计结果
                total_lines = sum(len(lines) for lines in results.values())
                processor.log(f"国家提取完成！处理 {processed_files} 个文件，失败 {failed_files} 个文件")
                processor.log(f"总共提取 {total_lines} 行记录")

                for filename, lines in results.items():
                    if lines:
                        processor.log(f"{filename}: {len(lines)} 条记录")
                    else:
                        processor.log(f"{filename}: 未找到匹配记录")

                processor.update_progress(100)
                
            except Exception as e:
                self.extraction_thread.update_signal.emit(f"处理文件时出现异常: {e}")
        
        # 创建线程实例
        self.extraction_thread = WorkerThread(extraction_task)
        self.extraction_thread.update_signal.connect(self.log_message)
        self.extraction_thread.progress_signal.connect(self.update_progress)
        self.extraction_thread.finished_signal.connect(lambda: self.log_message("国家提取处理完成！"))
        self.extraction_thread.start()
    
    # 第2部分: 删除apply等 (优化版)
    def process_remove_apply(self):
        if not self.input_file_paths:
            self.log_message("错误: 请先选择输入文件")
            return

        self.log_message("开始删除apply等关键词...")
        self.progress_bar.setValue(0)
        self.progress_label.setText("处理进度: 0%")
        
        # 定义每个文件对应的关键字
        file_keywords = {
            "us.txt": [
                "register","illinois.edu","Register","hbsp.harvard.edu",
                "academia.edu", "scratch.mit.edu", "uoc.edu", "aast.edu", 
                "utesa.edu", "gmail.com", "uniminuto.edu", "outlook.com", 
                "GMAIL.COM",  "yahoo.com", "hotmail.com", "apply", 
                "application", "nycenet.edu", "vccs.edu", "aku.edu", 
                "qou.edu", "upi.edu","ewubd.edu",".uopeople.edu", 
                "ppu.edu", "najah.edu", ".admissions.", ".sc.edu",
                "annauniv.edu", "cps.edu", "uwi.edu", "ateneo.edu", 
                "wqu.edu", "aucegypt.edu", "urbe.edu","upc.edu", 
                "uksw.edu", ".aust.edu", "amity.edu", "losrios.edu",
                "psu.edu","utsa.edu", ".csn.edu", "kongu.edu", 
                "ub.edu", ".minnstate.edu", "birzei..edu",
                "uniben.edu","aaup.edu","skku.","apus.edu","rutgers.edu",
                "alquds.edu","admission","secure.birds","uvmnet.edu","UNKNOWN",
                "jobs.","ccc.edu","neu.edu",".org",".com",".COM",".library",
                "library","ucam.edu","cuny.edu","sfcollege.edu","karunya.edu",
                "uta.edu","mdc.edu","byui.edu",".byu.edu",".uh.edu",
                "wichita.edu","fullerton.edu",".int","unt.edu",
                "ohio-state.edu", "ufl.edu",".com",".org",".net",".gov",
                "ucsb.edu","ucsb.edu","ucsc.edu","ucsd.edu","cmu.edu",
                ".authentichappiness",".online",".ncsu.edu",".unitec.edu",
                "fiu.edu",".kennesaw.edu","amherst.edu",".gmu.edu","fau.edu","bgsu.edu",
                "uky.edu",
                "calstate.edu","calstatela.edu",
                "tamuc.edu","tsu.edu",
                "@hotmail.com","@yahoo.com","@gmail.com","@outlook.com"
                "application","apply","admissions","register","asu.edu",
                "@hotmail.com","@yahoo.com","@gmail.com","@outlook.com",
                "scpdlogin.stanford.edu",".shib.", "shib.", "shibidp", "shibboleth",
                "shibboleth",".shib.", "shib.", "shibidp", "shibboleth",
            ],

            "hk.txt": [
                "application","register","apply","admissions",
                "@hotmail.com","@yahoo.com","@gmail.com","@outlook.com",
            ],

            "tw.txt": [
                "application","admissions","apply","register",
                "@hotmail.com","@yahoo.com","@gmail.com","@outlook.com",
            ],

            "uk.txt": [
                "application","admissions","apply","register",
                "@hotmail.com","@yahoo.com","@gmail.com","@outlook.com",
            ],
        }
        
        def filter_task(self=self):
            # 使用优化的文件处理器
            processor = OptimizedFileProcessor(
                progress_callback=lambda x: self.filter_thread.progress_signal.emit(x),
                log_callback=lambda x: self.filter_thread.update_signal.emit(x)
            )

            # 预编译关键词集合，提高查找效率
            keywords_set = set()
            for keywords_list in file_keywords.values():
                keywords_set.update(keywords_list)

            def process_input_file(file_path):
                """处理单个输入文件"""
                try:
                    filename = os.path.basename(file_path)
                    processor.log(f"处理输入文件: {filename}")

                    file_results = defaultdict(list)

                    # 使用优化的分块读取
                    for chunk in processor.read_file_chunked(file_path, CHUNK_SIZE):
                        for line in chunk:
                            # 快速分类到对应的国家文件
                            if '.edu.tw/' in line:
                                file_results['tw.txt'].append(line)
                            elif '.edu.hk/' in line:
                                file_results['hk.txt'].append(line)
                            elif '.edu/' in line:
                                file_results['us.txt'].append(line)
                            elif '.ac.uk/' in line:
                                file_results['uk.txt'].append(line)

                    return file_results, None
                except Exception as e:
                    return defaultdict(list), str(e)

            # 对于用户导入的文件，先处理它们
            if self.input_file_paths:
                processor.log(f"开始处理 {len(self.input_file_paths)} 个导入文件")

                # 并行处理输入文件
                input_results_list = processor.process_files_parallel(
                    self.input_file_paths,
                    process_input_file,
                    max_workers=min(4, MAX_WORKERS)
                )

                # 合并输入文件的结果并写入分类文件
                combined_results = defaultdict(list)
                for file_results, error in input_results_list:
                    if error:
                        processor.log(f"处理输入文件出错: {error}")
                    else:
                        for output_file, lines in file_results.items():
                            combined_results[output_file].extend(lines)

                # 写入分类文件
                for output_file, lines in combined_results.items():
                    if lines:
                        with open(output_file, 'a', encoding='utf-8', buffering=8192) as f:
                            f.writelines(lines)
                        processor.log(f"写入 {output_file}: {len(lines)} 行")

                processor.update_progress(50)

            # 然后处理生成的分类文件，使用优化的过滤方法
            def filter_file_content(file_path, keywords_to_remove):
                """过滤文件内容，移除包含关键词的行"""
                try:
                    processor.log(f"过滤文件: {file_path}")
                    filtered_lines = []

                    # 使用优化的分块读取
                    for chunk in processor.read_file_chunked(file_path, CHUNK_SIZE):
                        for line in chunk:
                            # 使用any()函数提高效率
                            if not any(keyword in line for keyword in keywords_to_remove):
                                filtered_lines.append(line)

                    return filtered_lines, None
                except Exception as e:
                    return [], str(e)

            # 并行处理分类文件
            files_to_process = []
            for source_file, keywords in file_keywords.items():
                if os.path.exists(source_file):
                    files_to_process.append((source_file, keywords))
                else:
                    processor.log(f"{source_file} 文件不存在")

            processor.log(f"开始过滤 {len(files_to_process)} 个分类文件")

            processed_count = 0
            total_files = len(files_to_process)

            for source_file, keywords in files_to_process:
                filtered_lines, error = filter_file_content(source_file, keywords)

                if error:
                    processor.log(f"处理 {source_file} 时出错: {error}")
                else:
                    # 写入过滤后的内容
                    try:
                        with open(source_file, 'w', encoding='utf-8', buffering=8192) as f:
                            f.writelines(filtered_lines)
                        processor.log(f"{source_file} 处理完成，保留了 {len(filtered_lines)} 行")
                    except Exception as e:
                        processor.log(f"写入 {source_file} 时出错: {e}")

                processed_count += 1
                progress = 50 + int((processed_count / total_files) * 50)
                processor.update_progress(min(99, progress))

            # 完成后更新进度为100%
            processor.update_progress(100)
            processor.log("删除apply等关键词处理完成！")
        
        # 创建线程实例
        self.filter_thread = WorkerThread(filter_task)
        self.filter_thread.update_signal.connect(self.log_message)
        self.filter_thread.progress_signal.connect(self.update_progress)
        self.filter_thread.finished_signal.connect(lambda: self.log_message("删除apply等关键词完成！"))
        self.filter_thread.start()
    
    # 第3部分: 后缀提取
    def process_suffix_extraction(self):
        if not os.path.exists("us.txt") and not os.path.exists("tw.txt") and not os.path.exists("hk.txt"):
            if self.input_file_paths:
                self.log_message("未找到分类文件，将首先从输入文件创建分类...")
                self.process_country_extraction()
            else:
                self.log_message("错误: 没有可处理的文件")
                return
        
        self.log_message("开始后缀提取处理...")
        self.progress_bar.setValue(0)
        self.progress_label.setText("处理进度: 0%")
        
        # 使用字典定义域名到文件名的映射
        domain_to_filename = {
            "cas.iu.edu": "iu.txt",
            "brandeis.edu": "brandeis.txt",
            "purdue.edu": "purdue.txt",
            "mypurdue.purdue.edu": "purdue_sso.txt",
            "calvin.edu": "calvin.txt",
            ".utexas.edu": "utexas.txt",
            "cornell.edu": "cornell.txt",
            "fed.princeton.edu": "fed.txt",
            "alum.mit.edu": "mit-alumni.txt",
            "login.stanford.edu": "stanford.txt",
            "secure.its.yale.edu": "yale.txt",
            "alumsso.mit.edu": "mit_alumni.txt",
            "sso.brown.edu": "brown.txt",
            "alumni.stanford.edu": "stanford_alumni.txt",
            "fightonline.usc.edu": "usc_fightonline.txt",
            "nau.edu": "nau.txt",
            "secure.yale.imodules.com": "yale_alumni.txt",
            "ucdavis.edu": "ucdavis.txt",
            ".gsu.edu": "gsu.txt",
            "tamu.edu": "tamu.txt",
            "auth.berkeley.edu": "berkeley.txt",
            "temple.edu": "temple.txt",
            "login.harvard.edu": "harvard_login.txt",
            "key.harvard.edu": "harvard_key.txt",
            "weblogin.umich.edu": "umich.txt",
            "access.caltech.edu": "caltech.txt",
            "rice.edu": "rice.txt",
            ".oregonstate.edu": "oregonstate.txt",
            "umbc.edu": "umbc.txt",
            "utah.edu": "utah.txt",
            "cmich.edu": "cmich.txt",
            "kent.edu": "kent.txt",
            ".tsu.edu": "tsu.txt",
            "csun.edu": "csun.txt",
            "washington.edu": "washington.txt",
            "shib.bu.edu": "bu.txt",
            "upenn.edu": "upenn.txt",
            "usf.edu": "usf.txt",
            "uga.edu": "uga.txt",
            "umn.edu": "umn.txt",
            "oregonstate.edu": "oregonstate.txt",
            "passport.pitt.edu": "pitt.txt",
            "utdallas.edu": "utdallas.txt",
            "njit.edu": "njit.txt",
            "depaul.edu": "depaul.txt",
            "osu.edu": "osu.txt",
            "login.usc.edu": "usc.txt",
            "arizona.edu": "arizona.txt",
            "nyu.edu": "nyu.txt",
            ".uconn.edu": "uconn.txt",
            ".duke.edu": "duke.txt",
            
            
        }

        keywords_to_files = {
            "美国": {
                "file": "us.txt",
                "keywords": list(domain_to_filename.keys())
            },
            "台湾": {
                "file": "tw.txt",
                "keywords": [           
                    "fcu.edu.tw",
                    "fju.edu.tw",
                    "ndhu.edu.tw",
                    "ntua.edu.tw",
                    "stust.edu.tw",
                ]
            },
            "香港": {
                "file": "hk.txt",
                "keywords": [
                    "polyu.edu.hk",
                    "adfs.hku.hk",
                    "hkuportal.hku.hk",
                ]
            },
            "英国": {
                "file": "uk.txt",
                "keywords": [
                    "cam.ac.uk",
                    "alumni.cam.ac.uk",
                    "ox.ac.uk",
                    "alumniweb.ox.ac.uk",
                    
                ]
            },
        }
        
        def extraction_task(self=self):
            total_regions = len(keywords_to_files)
            processed_regions = 0
            
            for region, info in keywords_to_files.items():
                source_file = info["file"]
                keywords = info["keywords"]
                
                if not os.path.exists(source_file):
                    self.suffix_thread.update_signal.emit(f"{source_file} 不存在，跳过...")
                    processed_regions += 1
                    progress = int((processed_regions / total_regions) * 100)
                    self.suffix_thread.progress_signal.emit(progress)
                    continue
                
                try:
                    with open(source_file, 'r', encoding='utf-8') as file:
                        lines = file.readlines()
                    
                    # 使用字典存储按文件名分类的行
                    output_files = {}
                    remaining_lines = []
                    
                    total_lines = len(lines)
                    for i, line in enumerate(lines):
                        if i % 100 == 0 or i == total_lines - 1:
                            within_file_progress = (i / total_lines) * 0.8
                            region_progress = (processed_regions / total_regions)
                            total_progress = int(((region_progress + (within_file_progress / total_regions)) * 100))
                            self.suffix_thread.progress_signal.emit(min(95, total_progress))
                            
                        matched = False
                        for keyword in keywords:
                            if keyword in line:
                                # 使用映射字典获取输出文件名
                                output_filename = domain_to_filename.get(keyword, keyword.split('.')[0] + '.txt')
                                if output_filename not in output_files:
                                    output_files[output_filename] = []
                                output_files[output_filename].append(line)
                                matched = True
                                break
                        if not matched:
                            remaining_lines.append(line)
                    
                    # 文件写入操作
                    self.suffix_thread.progress_signal.emit(min(95, int((processed_regions / total_regions * 100) + 5)))
                    
                    for filename, matched_lines in output_files.items():
                        if matched_lines:
                            with open(filename, 'w', encoding='utf-8') as file:
                                file.writelines(matched_lines)
                            self.suffix_thread.update_signal.emit(f"已创建 {filename}，包含 {len(matched_lines)} 行")
                    
                    with open(source_file, 'w', encoding='utf-8') as file:
                        file.writelines(remaining_lines)
                    
                    self.suffix_thread.update_signal.emit(f"{source_file} 处理完成，剩余 {len(remaining_lines)} 行")
                except Exception as e:
                    self.suffix_thread.update_signal.emit(f"处理 {source_file} 时出错: {e}")
                
                processed_regions += 1
                progress = int((processed_regions / total_regions) * 100)
                self.suffix_thread.progress_signal.emit(progress)
            
            self.suffix_thread.progress_signal.emit(100)
        
        # 创建线程实例
        self.suffix_thread = WorkerThread(extraction_task)
        self.suffix_thread.update_signal.connect(self.log_message)
        self.suffix_thread.progress_signal.connect(self.update_progress)
        self.suffix_thread.finished_signal.connect(lambda: self.log_message("后缀提取处理完成！"))
        self.suffix_thread.start()
    
    # 第4部分: 冒号替换
    def process_colon_replacement(self):
        self.log_message("开始冒号替换处理...")
        self.progress_bar.setValue(0)
        self.progress_label.setText("处理进度: 0%")
        
        # 要处理的文件列表
        files = [
            'usc_fightonline.txt',
            'stanford_alumni.txt',
            'brown.txt',
            'mit_alumni.txt',
            'yale.txt',
            'stanford.txt',
            'mit-alumni.txt',
            'fed.txt',
            'utexas.txt',
            'nau.txt',
            'cornell.txt',
            'polyu.txt',
            'fcu.txt',
            'yale_alumni.txt',
            'alum.txt',
            'adfs.txt',
            'hkuportal.txt',
            'alumsso.txt',
            'auth.txt',
            'brandeis.txt',
            'bu.txt',
            'calvin.txt',
            'cas.txt',
            'cmich.txt',
            'cornell.txt',
            'csun.txt',
            'depaul.txt',
            'fcu.txt',
            'fed.txt',
            'fju.txt',
            'gsu.txt',
            'hku.txt',
            'kent.txt',
            'key.txt',
            'login.txt',
            'nau.txt',
            'ndhu.txt',
            'njit.txt',
            'ntua.txt',
            'oregonstate.txt',
            'osu.txt',
            'passport.txt',
            'pin1.txt',
            'polyu.txt',
            'purdue.txt',
            'purdue_sso.txt',
            'rice.txt',
            'sso.txt',
            'stust.txt',
            'tamu.txt',
            'temple.txt',
            'ucdavis.txt',
            'uga.txt',
            'umbc.txt',
            'umn.txt',
            'upenn.txt',
            'usf.txt',
            'utah.txt',
            'utdallas.txt',
            'utexas.txt',
            'washington.txt',
            'weblogin.txt',
            'yale.txt',
            "usc.txt",
            "arizona.txt",
            "nyu.txt",
            "uconn.txt",
            "duke.txt",
        ]  
        
        def replacement_task(self=self):
            total_files = len(files)
            processed_files = 0
            skipped_files = 0
            
            # 首先检查是否有导入的文件需要处理
            if self.input_file_paths:
                # 如果有导入的文件并且没有上一步分类的结果,先执行国家提取和后缀提取
                if not os.path.exists('us.txt') and not os.path.exists('tw.txt') and not os.path.exists('hk.txt'):
                    self.replacement_thread.update_signal.emit("没有找到分类文件,先进行国家提取...")
                    # 这里只发出信号,不实际执行,因为在线程中无法直接调用其他处理函数
                    return
            
            for file_name in files:
                if os.path.exists(file_name):
                    try:
                        with open(file_name, 'r', encoding='utf-8', errors='ignore') as file:
                            content = file.read()
                        
                        # 替换内容中的竖线为冒号
                        updated_content = content.replace('|', ':')
                        updated_content = updated_content.replace(' ', ':')
                        updated_content = updated_content.replace('/', ':')
                        
                        with open(file_name, 'w', encoding='utf-8') as file:
                            file.write(updated_content)
                        
                        self.replacement_thread.update_signal.emit(f"{file_name} 处理完成")
                        processed_files += 1
                    except Exception as e:
                        self.replacement_thread.update_signal.emit(f"处理 {file_name} 时出错: {e}")
                else:
                    self.replacement_thread.update_signal.emit(f"{file_name} 不存在，跳过")
                    skipped_files += 1
                
                # 确保进度条不超过99%，留出1%在处理完成后设置为100%
                progress = min(99, int((processed_files / total_files) * 100))
                self.replacement_thread.progress_signal.emit(progress)
            
            # 完成后确保进度条为100%
            self.replacement_thread.progress_signal.emit(100)
            
            # 总结处理结果
            self.replacement_thread.update_signal.emit(
                f"冒号替换处理完成! 成功处理 {processed_files} 个文件, 跳过 {skipped_files} 个文件。")
        
        # 创建线程实例
        self.replacement_thread = WorkerThread(replacement_task)
        self.replacement_thread.update_signal.connect(self.log_message)
        self.replacement_thread.progress_signal.connect(self.update_progress)
        self.replacement_thread.finished_signal.connect(lambda: self.log_message("冒号替换处理完成！"))
        self.replacement_thread.start()
    
    # 第5部分: 删除链接
    def process_remove_links(self):
        self.log_message("开始删除链接处理...")
        self.progress_bar.setValue(0)
        self.progress_label.setText("处理进度: 0%")
        
        # 定义文件路径列表
        file_paths = [
            'usc_fightonline.txt',
            'stanford_alumni.txt',
            'brown.txt',
            'mit_alumni.txt',
            'yale.txt',
            'stanford.txt',
            'mit-alumni.txt',
            'fed.txt',
            'utexas.txt',
            'nau.txt',
            'cornell.txt',
            'polyu.txt',
            'fcu.txt',
            'yale_alumni.txt',
            'cuhk_links_result.txt',
            'cityu.txt',
            'adfs.txt',
            'hkuportal.txt',
            'alum.txt',
            'alumsso.txt',
            'auth.txt',
            'brandeis.txt',
            'bu.txt',
            'calvin.txt',
            'cas.txt',
            'cmich.txt',
            'cornell.txt',
            'csun.txt',
            'depaul.txt',
            'fcu.txt',
            'fed.txt',
            'fju.txt',
            'gsu.txt',
            'hku.txt',
            'kent.txt',
            'key.txt',
            'login.txt',
            'nau.txt',
            'nau_cas.txt',
            'ndhu.txt',
            'njit.txt',
            'ntua.txt',
            'oregonstate.txt',
            'osu.txt',
            'passport.txt',
            'pin1.txt',
            'polyu.txt',
            'purdue.txt',
            'purdue_sso.txt',
            'rice.txt',
            'sso.txt',
            'stust.txt',
            'tamu.txt',
            'temple.txt', 
            'ucdavis.txt',
            'uga.txt',  
            'umbc.txt',
            'umn.txt',
            'upenn.txt',
            'usf.txt',
            'utah.txt',
            'utdallas.txt',
            'utexas.txt',
            'washington.txt',
            'weblogin.txt',
            'yale.txt',
            "usc.txt",
            "arizona.txt",
            "nyu.txt",
            "uconn.txt",
            "duke.txt",
        ]
        
        def link_removal_task(self=self):
            total_files = len(file_paths)
            processed_files = 0
            skipped_files = 0
            
            # 首先检查是否需要执行前面的处理步骤
            files_exist = False
            for file_path in file_paths:
                if os.path.exists(file_path):
                    files_exist = True
                    break
            
            if not files_exist and self.input_file_paths:
                self.link_removal_thread.update_signal.emit("未找到需要处理的文件，建议先执行前面的处理步骤")
            
            for file_path in file_paths:
                if not os.path.exists(file_path):
                    self.link_removal_thread.update_signal.emit(f"文件 '{file_path}' 不存在，跳过。")
                    skipped_files += 1
                else:
                    try:
                        with open(file_path, 'r', encoding='utf-8', errors='ignore') as file:
                            lines = file.readlines()
                        
                        processed_lines = []
                        total_lines = len(lines)
                        
                        if total_lines == 0:
                            self.link_removal_thread.update_signal.emit(f"文件 '{file_path}' 为空，跳过。")
                            skipped_files += 1
                            continue
                        
                        for i, line in enumerate(lines):
                            # 更新子任务进度
                            if i % 100 == 0 or i == total_lines - 1:
                                sub_progress = int((i / total_lines) * 100)
                                total_progress = int((processed_files / total_files * 100) + 
                                                  (sub_progress / total_files))
                                # 确保进度条不超过99%
                                self.link_removal_thread.progress_signal.emit(min(99, total_progress))
                            
                            try:
                                last_colon_index = line.rfind(':')
                                second_last_colon_index = line.rfind(':', 0, last_colon_index)
                                
                                if second_last_colon_index != -1:
                                    processed_line = line[second_last_colon_index + 1:]
                                else:
                                    processed_line = line
                                
                                processed_lines.append(processed_line)
                            except Exception as e:
                                # 处理单行异常但继续处理
                                processed_lines.append(line)  # 保留原行
                        
                        with open(file_path, 'w', encoding='utf-8') as file:
                            file.writelines(processed_lines)
                        
                        self.link_removal_thread.update_signal.emit(f"文件 '{file_path}' 处理完成。")
                        processed_files += 1
                    except Exception as e:
                        self.link_removal_thread.update_signal.emit(f"处理 {file_path} 时出错: {e}")
                
                # 确保进度值不超过99%
                progress = min(99, int((processed_files / total_files) * 100))
                self.link_removal_thread.progress_signal.emit(progress)
            
            # 完成后确保进度条为100%
            self.link_removal_thread.progress_signal.emit(100)
            
            # 总结处理结果
            self.link_removal_thread.update_signal.emit(
                f"删除链接处理完成! 成功处理 {processed_files} 个文件, 跳过 {skipped_files} 个文件。")
        
        # 创建线程实例
        self.link_removal_thread = WorkerThread(link_removal_task)
        self.link_removal_thread.update_signal.connect(self.log_message)
        self.link_removal_thread.progress_signal.connect(self.update_progress)
        self.link_removal_thread.finished_signal.connect(lambda: self.log_message("删除链接处理完成！"))
        self.link_removal_thread.start()
    
    # 第6部分: 去重处理 (优化版)
    def process_remove_duplicates(self):
        self.log_message("开始去重处理...")
        self.progress_bar.setValue(0)
        self.progress_label.setText("处理进度: 0%")
        
        file_paths = [
            'usc_fightonline.txt',
            'stanford_alumni.txt',
            'brown.txt',
            'mit_alumni.txt',
            'yale.txt',
            'stanford.txt',
            'mit-alumni.txt',
            'fed.txt',
            'utexas.txt',
            'nau.edu',
            'cornell.txt',
            'polyu.txt',
            'fcu.txt',
            'yale_alumni.txt',
            'cuhk_links_result.txt',
            'cityu.txt',
            'adfs.txt',
            'hkuportal.txt',
            'alum.txt',
            'alumni.txt',
            'alumsso.txt',
            'auth.txt',
            'berkeley.txt',
            'brandeis.txt',
            'bu.txt',
            'calvin.txt',
            'cas.txt',
            'cmich.txt',
            'cornell.txt',
            'csun.txt',
            'depaul.txt',
            'fcu.txt',
            'fed.txt',
            'fju.txt',
            'gsu.txt',
            'hku.txt',
            'kent.txt',
            'key.txt',
            'login.txt',
            'nau.txt',
            'nau_cas.txt',
            'ndhu.txt',
            'njit.txt',
            'ntua.txt',
            'oregonstate.txt',
            'osu.txt',
            'passport.txt',
            'pin1.txt',
            'polyu.txt',
            'purdue.txt',
            'purdue_sso.txt',
            'rice.txt',
            'sso.txt',
            'stust.txt',
            'tamu.txt',
            'temple.txt',
            'ucdavis.txt',
            'uga.txt',
            'umbc.txt',
            'umn.txt',
            'upenn.txt',
            'usf.txt',
            'utah.txt',
            'utdallas.txt',
            'utexas.txt',
            'washington.txt',
            'weblogin.txt',
            'yale.txt',
            "usc.txt",
            "arizona.txt",
            "nyu.txt",
            "uconn.txt",
            "duke.txt",
        ]
        
        def deduplication_task(self=self):
            total_files = len(file_paths)
            processed_files = 0
            skipped_files = 0
            total_lines_before = 0
            total_lines_after = 0
            
            for file_path in file_paths:
                if not os.path.exists(file_path):
                    self.deduplication_thread.update_signal.emit(f"文件 '{file_path}' 不存在，跳过。")
                    skipped_files += 1
                else:
                    try:
                        with open(file_path, 'r', encoding='utf-8', errors='ignore') as file:
                            lines = file.readlines()
                        
                        lines_count_before = len(lines)
                        total_lines_before += lines_count_before
                        
                        if lines_count_before == 0:
                            self.deduplication_thread.update_signal.emit(f"文件 '{file_path}' 为空，跳过。")
                            skipped_files += 1
                            continue
                        
                        # 更新进度到50%，表示读取完成
                        sub_progress = 50
                        total_progress = int((processed_files / total_files * 100) + 
                                           (sub_progress / total_files))
                        # 确保进度条不超过99%
                        self.deduplication_thread.progress_signal.emit(min(99, total_progress))
                        
                        # 使用集合去重，保持原有顺序
                        unique_lines = []
                        seen = set()
                        for line in lines:
                            line_stripped = line.strip()
                            if line_stripped and line_stripped not in seen:
                                seen.add(line_stripped)
                                unique_lines.append(line)
                        
                        lines_count_after = len(unique_lines)
                        total_lines_after += lines_count_after
                        
                        with open(file_path, 'w', encoding='utf-8') as file:
                            file.writelines(unique_lines)
                        
                        removed_count = lines_count_before - lines_count_after
                        self.deduplication_thread.update_signal.emit(
                            f"{file_path} 去重完成: 从 {lines_count_before} 条记录中删除了 {removed_count} 条重复项，剩余 {lines_count_after} 条。")
                        processed_files += 1
                    except Exception as e:
                        self.deduplication_thread.update_signal.emit(f"处理 {file_path} 时出错: {e}")
                
                # 确保进度值不超过99%
                progress = min(99, int((processed_files / total_files) * 100))
                self.deduplication_thread.progress_signal.emit(progress)
            
            # 处理完成后显示100%
            self.deduplication_thread.progress_signal.emit(100)
            
            # 总结处理结果
            total_removed = total_lines_before - total_lines_after
            self.deduplication_thread.update_signal.emit(
                f"去重处理完成! 成功处理 {processed_files} 个文件, 跳过 {skipped_files} 个文件。"
                f"总共删除了 {total_removed} 条重复记录。")
        
        # 创建线程实例
        self.deduplication_thread = WorkerThread(deduplication_task)
        self.deduplication_thread.update_signal.connect(self.log_message)
        self.deduplication_thread.progress_signal.connect(self.update_progress)
        self.deduplication_thread.finished_signal.connect(lambda: self.log_message("去重处理完成！"))
        self.deduplication_thread.start()

if __name__ == '__main__':
    app = QApplication(sys.argv)
    window = EduDataProcessor()
    window.show()
    sys.exit(app.exec()) 