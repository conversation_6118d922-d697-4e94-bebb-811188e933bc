#!/usr/bin/env python
# -*- coding: utf-8 -*-

class LexicalAnalyzer:
    def __init__(self):
        # 关键字表
        self.KEY_WORDS = ["main", "int", "char", "if", "else", "for", "while"]
        # 对应的种别编码
        self.KEY_WORD_CODES = {
            "main": 1,
            "int": 2,
            "char": 3,
            "if": 4,
            "else": 5,
            "for": 6,
            "while": 7
        }
        # 算符和界符的种别编码
        self.SYMBOL_CODES = {
            "=": 21, "+": 22, "-": 23, "*": 24, "/": 25,
            "(": 26, ")": 27, "[": 28, "]": 29, "{": 30,
            "}": 31, ",": 32, ":": 33, ";": 34, ">": 35,
            "<": 36, ">=": 37, "<=": 38, "==": 39, "!=": 40,
            "&": 41, "&&": 42, "||": 43
        }
        self.line = 1
        self.column = 0
        self.output_file = None

    def open_output_file(self, filename):
        self.output_file = open(filename, 'w', encoding='utf-8')

    def close_output_file(self):
        if self.output_file:
            self.output_file.close()

    def print_error(self, char):
        print(f"错误：无法识别的字符 '{char}' 在行 {self.line}, 列 {self.column}")

    def write_token(self, code, value):
        """将识别的单词符号写入到输出文件中"""
        if isinstance(value, int):
            token_str = f"({code}, {value})"
        else:
            token_str = f"({code}, {value})"
        self.output_file.write(token_str + "  ")
        print(token_str, end="  ")

    def scanner(self, source_program):
        """词法分析器的主函数，对源程序进行扫描，识别单词符号"""
        i = 0
        length = len(source_program)
        
        while i < length:
            char = source_program[i]
            self.column += 1
            
            # 处理空白字符
            if char.isspace():
                if char == '\n':
                    self.line += 1
                    self.column = 0
                i += 1
                continue
            
            # 处理标识符和关键字
            if char.isalpha():
                start = i
                while i < length and (source_program[i].isalnum() or source_program[i] == '_'):
                    i += 1
                token = source_program[start:i]
                
                # 判断是关键字还是标识符
                if token in self.KEY_WORDS:
                    code = self.KEY_WORD_CODES[token]
                    self.write_token(code, token)
                else:
                    self.write_token(10, token)  # 标识符的种别码为10
                continue
            
            # 处理数字
            if char.isdigit():
                start = i
                while i < length and source_program[i].isdigit():
                    i += 1
                token = source_program[start:i]
                self.write_token(20, int(token))  # 整型常数的种别码为20
                continue
            
            # 处理两个字符的算符
            if i + 1 < length:
                two_char = source_program[i:i+2]
                if two_char in [">=", "<=", "==", "!=", "&&", "||"]:
                    self.write_token(self.SYMBOL_CODES[two_char], two_char)
                    i += 2
                    continue
            
            # 处理单个字符的算符和界符
            if char in self.SYMBOL_CODES:
                self.write_token(self.SYMBOL_CODES[char], char)
                i += 1
                continue
            
            # 无法识别的字符
            self.print_error(char)
            i += 1
    
    def analyze(self, source_file, output_file):
        """分析源文件并输出结果到指定文件"""
        try:
            with open(source_file, 'r', encoding='utf-8') as f:
                source_program = f.read()
            
            self.open_output_file(output_file)
            self.scanner(source_program)
            self.close_output_file()
            
            print(f"\n词法分析完成，结果已保存至 {output_file}")
        except Exception as e:
            print(f"分析过程发生错误: {e}")

if __name__ == "__main__":
    # 测试用例
    test_program = """main ()
{
int i = 10;
while(i) i = i - 1;
}"""
    
    analyzer = LexicalAnalyzer()
    
    # 将测试程序写入临时文件
    with open("test_program.c", "w", encoding="utf-8") as f:
        f.write(test_program)
    
    # 分析测试程序
    analyzer.analyze("test_program.c", "output.txt") 