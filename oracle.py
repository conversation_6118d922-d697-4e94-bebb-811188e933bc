def process_files(source_files, keywords_files):
    for source_file_path in source_files:
        with open(source_file_path, 'r', encoding='utf-8', errors='ignore') as source_file:
            file_handles = {}
            for line in source_file:
                for keyword, filename in keywords_files.items():
                    if keyword in line:
                        if filename not in file_handles:
                            file_handles[filename] = open(filename, 'w', encoding='utf-8')
                        file_handles[filename].write(line)
            for file in file_handles.values():
                file.close()

# 定义关键字与文件名的映射关系
keywords_files = {
    "https://idcs-": "oracle.txt",
    "academy.oracle.com": "orac_academy.txt",
}

# 假设有多个源文件需要处理
source_files = [
    '269.txt',
]

# 调用函数处理多个源文件
process_files(source_files, keywords_files)

print("处理完成。")
