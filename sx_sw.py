#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
筛选邮箱@前面为四位字符的邮箱脚本
从msn.txt文件中筛选出邮箱地址@符号前面恰好为4位字符的邮箱
"""

import re
import os

def filter_four_char_emails(input_file='mail.txt', output_file='four_char_emails.txt'):
    """
    从输入文件中筛选邮箱@前面为四位字符的邮箱
    
    Args:
        input_file (str): 输入文件名，默认为'msn.txt'
        output_file (str): 输出文件名，默认为'four_char_emails.txt'
    """
    
    # 检查输入文件是否存在
    if not os.path.exists(input_file):
        print(f"错误：文件 {input_file} 不存在！")
        return
    
    # 正则表达式匹配邮箱@前面恰好为4位字符的邮箱
    # ^.{4}@ 表示行开始后恰好4个字符然后是@符号
    # 但考虑到文件格式，我们需要匹配整行中的邮箱部分
    email_pattern = r'\b[a-zA-Z0-9_]{4}@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}\b'
    
    four_char_emails = []
    total_lines = 0
    matched_lines = 0
    
    try:
        with open(input_file, 'r', encoding='utf-8', errors='ignore') as file:
            for line_num, line in enumerate(file, 1):
                total_lines += 1
                line = line.strip()
                
                # 跳过空行
                if not line:
                    continue
                
                # 查找邮箱地址
                emails = re.findall(r'\b[a-zA-Z0-9_.-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}\b', line)
                
                for email in emails:
                    # 检查@前面是否恰好为4位字符
                    username = email.split('@')[0]
                    if len(username) == 4:
                        four_char_emails.append(line)
                        matched_lines += 1
                        print(f"找到匹配邮箱: {email}")
                        break  # 每行只记录一次
        
        # 保存结果到输出文件
        with open(output_file, 'w', encoding='utf-8') as output:
            output.write(f"# 筛选出的邮箱@前面为四位字符的邮箱记录\n")
            output.write(f"# 总共处理行数: {total_lines}\n")
            output.write(f"# 匹配记录数: {matched_lines}\n")
            output.write(f"# 生成时间: {__import__('datetime').datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            for email_line in four_char_emails:
                output.write(email_line + '\n')
        
        print(f"\n筛选完成！")
        print(f"总共处理了 {total_lines} 行")
        print(f"找到 {matched_lines} 个匹配的邮箱记录")
        print(f"结果已保存到 {output_file}")
        
        # 显示前10个匹配的邮箱作为预览
        if four_char_emails:
            print(f"\n前10个匹配的邮箱记录预览:")
            for i, email_line in enumerate(four_char_emails[:10], 1):
                print(f"{i:2d}. {email_line}")
            
            if len(four_char_emails) > 10:
                print(f"... 还有 {len(four_char_emails) - 10} 个记录")
    
    except Exception as e:
        print(f"处理文件时出错: {e}")

def main():
    """主函数"""
    print("=" * 60)
    print("邮箱筛选工具 - 筛选@前面为四位字符的邮箱")
    print("=" * 60)
    
    # 执行筛选
    filter_four_char_emails()
    
    print("\n程序执行完毕！")

if __name__ == "__main__":
    main()
