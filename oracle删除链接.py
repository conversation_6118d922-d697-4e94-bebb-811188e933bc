# 定义文件路径列表
file_paths = [
    # 'oracle.txt',
    'member.txt',
    # 'profile.txt',
    ]

# 定义处理文件的函数
def process_file(file_path):
    with open(file_path, 'r', encoding='utf-8') as file:
        lines = file.readlines()
    
    processed_lines = []
    for line in lines:
        last_colon_index = line.rfind(':')
        second_last_colon_index = line.rfind(':', 0, last_colon_index)
        
        if second_last_colon_index != -1:
            processed_line = line[second_last_colon_index + 1:]
        else:
            processed_line = line
        
        processed_lines.append(processed_line)
    
    with open(file_path, 'w', encoding='utf-8') as file:
        file.writelines(processed_lines)

    print(f"文件 '{file_path}' 处理完成。")

# 处理每个文件
for file_path in file_paths:
    process_file(file_path)
