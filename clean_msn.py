#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
删除msn.txt文件中所有邮箱前面的数字
"""

import re

def clean_msn_file():
    """删除msn.txt文件中所有邮箱前面的数字"""
    
    input_file = 'msn.txt'
    output_file = 'msn_cleaned.txt'
    
    try:
        with open(input_file, 'r', encoding='utf-8', errors='ignore') as file:
            lines = file.readlines()
        
        cleaned_lines = []
        processed_count = 0
        
        for line in lines:
            original_line = line.strip()
            
            # 跳过空行
            if not original_line:
                cleaned_lines.append('')
                continue
            
            # 使用正则表达式删除前面的数字和冒号
            # 匹配格式: "数字: 内容" -> "内容"
            cleaned_line = re.sub(r'^\d+:\s*', '', original_line)
            cleaned_lines.append(cleaned_line)
            
            if cleaned_line != original_line:
                processed_count += 1
        
        # 保存清理后的结果
        with open(output_file, 'w', encoding='utf-8') as output:
            for line in cleaned_lines:
                output.write(line + '\n')
        
        print(f"处理完成！")
        print(f"总共处理了 {len(lines)} 行")
        print(f"成功清理了 {processed_count} 行的数字前缀")
        print(f"结果已保存到 {output_file}")
        
        # 显示前5行对比
        print(f"\n前5行处理结果:")
        with open(input_file, 'r', encoding='utf-8', errors='ignore') as file:
            original_lines = file.readlines()[:5]
        
        for i, (orig, clean) in enumerate(zip(original_lines, cleaned_lines[:5]), 1):
            if orig.strip():
                print(f"{i}. 原始: {orig.strip()}")
                print(f"   清理: {clean}")
                print()
    
    except Exception as e:
        print(f"处理文件时出错: {e}")

if __name__ == "__main__":
    clean_msn_file()
