#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PyQt5文本筛选工具
功能：从输入文件中筛选出包含特定关键词的所有行
"""

import sys
import os
from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout,
                             QWidget, QPushButton, QLineEdit, QTextEdit, QLabel,
                             QFileDialog, QMessageBox, QCheckBox, QSpinBox,
                             QGroupBox, QProgressBar)
from PyQt5.QtCore import Qt, QThread, pyqtSignal


class FilterWorker(QThread):
    """后台筛选工作线程"""
    progress = pyqtSignal(int)
    finished = pyqtSignal(list, int)
    error = pyqtSignal(str)
    
    def __init__(self, file_path, keywords, case_sensitive=False, whole_word=False):
        super().__init__()
        self.file_path = file_path
        self.keywords = keywords
        self.case_sensitive = case_sensitive
        self.whole_word = whole_word
    
    def run(self):
        try:
            filtered_lines = []
            total_lines = 0
            
            # 首先计算总行数
            with open(self.file_path, 'r', encoding='utf-8', errors='ignore') as file:
                total_lines = sum(1 for _ in file)
            
            # 处理关键词
            if not self.case_sensitive:
                keywords = [kw.lower() for kw in self.keywords]
            else:
                keywords = self.keywords
            
            # 筛选行
            with open(self.file_path, 'r', encoding='utf-8', errors='ignore') as file:
                for line_num, line in enumerate(file, 1):
                    original_line = line.rstrip('\n\r')
                    search_line = line if self.case_sensitive else line.lower()
                    
                    # 检查是否包含任何关键词
                    found = False
                    for keyword in keywords:
                        if self.whole_word:
                            # 全词匹配
                            import re
                            pattern = r'\b' + re.escape(keyword) + r'\b'
                            if re.search(pattern, search_line):
                                found = True
                                break
                        else:
                            # 部分匹配
                            if keyword in search_line:
                                found = True
                                break
                    
                    if found:
                        filtered_lines.append(f"{line_num}: {original_line}")
                    
                    # 更新进度
                    if line_num % 100 == 0:
                        progress = int((line_num / total_lines) * 100)
                        self.progress.emit(progress)
            
            self.finished.emit(filtered_lines, total_lines)
            
        except Exception as e:
            self.error.emit(str(e))


class TextFilterApp(QMainWindow):
    def __init__(self):
        super().__init__()
        self.init_ui()
        self.file_path = ""
        
    def init_ui(self):
        self.setWindowTitle("文本筛选工具 - 关键词筛选器")
        self.setGeometry(100, 100, 800, 600)
        
        # 创建中央窗口部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        
        # 文件选择区域
        file_group = QGroupBox("文件选择")
        file_layout = QHBoxLayout(file_group)
        
        self.file_label = QLabel("未选择文件")
        self.file_label.setFrameStyle(QLabel.Panel | QLabel.Sunken)
        self.browse_btn = QPushButton("浏览文件")
        self.browse_btn.clicked.connect(self.browse_file)
        
        file_layout.addWidget(self.file_label, 1)
        file_layout.addWidget(self.browse_btn)
        
        # 关键词输入区域
        keyword_group = QGroupBox("关键词设置")
        keyword_layout = QVBoxLayout(keyword_group)
        
        keyword_input_layout = QHBoxLayout()
        keyword_input_layout.addWidget(QLabel("关键词:"))
        self.keyword_input = QLineEdit()
        self.keyword_input.setPlaceholderText("输入关键词，多个关键词用逗号分隔")
        keyword_input_layout.addWidget(self.keyword_input)
        
        # 选项设置
        options_layout = QHBoxLayout()
        self.case_sensitive_cb = QCheckBox("区分大小写")
        self.whole_word_cb = QCheckBox("全词匹配")
        options_layout.addWidget(self.case_sensitive_cb)
        options_layout.addWidget(self.whole_word_cb)
        options_layout.addStretch()
        
        keyword_layout.addLayout(keyword_input_layout)
        keyword_layout.addLayout(options_layout)
        
        # 操作按钮区域
        button_layout = QHBoxLayout()
        self.filter_btn = QPushButton("开始筛选")
        self.filter_btn.clicked.connect(self.start_filter)
        self.clear_btn = QPushButton("清空结果")
        self.clear_btn.clicked.connect(self.clear_results)
        self.save_btn = QPushButton("保存结果")
        self.save_btn.clicked.connect(self.save_results)
        self.save_btn.setEnabled(False)
        
        button_layout.addWidget(self.filter_btn)
        button_layout.addWidget(self.clear_btn)
        button_layout.addWidget(self.save_btn)
        button_layout.addStretch()
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        
        # 结果显示区域
        result_group = QGroupBox("筛选结果")
        result_layout = QVBoxLayout(result_group)
        
        self.result_info = QLabel("等待筛选...")
        self.result_text = QTextEdit()
        self.result_text.setReadOnly(True)
        
        result_layout.addWidget(self.result_info)
        result_layout.addWidget(self.result_text)
        
        # 添加所有组件到主布局
        main_layout.addWidget(file_group)
        main_layout.addWidget(keyword_group)
        main_layout.addLayout(button_layout)
        main_layout.addWidget(self.progress_bar)
        main_layout.addWidget(result_group, 1)
        
        # 使用PyQt5默认样式，移除自定义样式
    
    def browse_file(self):
        """浏览并选择文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, 
            "选择要筛选的文件", 
            "", 
            "文本文件 (*.txt);;所有文件 (*.*)"
        )
        
        if file_path:
            self.file_path = file_path
            self.file_label.setText(os.path.basename(file_path))
            self.file_label.setToolTip(file_path)
    
    def start_filter(self):
        """开始筛选"""
        if not self.file_path:
            QMessageBox.warning(self, "警告", "请先选择要筛选的文件！")
            return
        
        if not os.path.exists(self.file_path):
            QMessageBox.warning(self, "警告", "选择的文件不存在！")
            return
        
        keywords_text = self.keyword_input.text().strip()
        if not keywords_text:
            QMessageBox.warning(self, "警告", "请输入要筛选的关键词！")
            return
        
        # 解析关键词
        keywords = [kw.strip() for kw in keywords_text.split(',') if kw.strip()]
        if not keywords:
            QMessageBox.warning(self, "警告", "请输入有效的关键词！")
            return
        
        # 禁用按钮，显示进度条
        self.filter_btn.setEnabled(False)
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        self.result_info.setText("正在筛选中...")
        
        # 创建并启动工作线程
        self.worker = FilterWorker(
            self.file_path, 
            keywords,
            self.case_sensitive_cb.isChecked(),
            self.whole_word_cb.isChecked()
        )
        self.worker.progress.connect(self.update_progress)
        self.worker.finished.connect(self.filter_finished)
        self.worker.error.connect(self.filter_error)
        self.worker.start()
    
    def update_progress(self, value):
        """更新进度条"""
        self.progress_bar.setValue(value)
    
    def filter_finished(self, filtered_lines, total_lines):
        """筛选完成"""
        self.filter_btn.setEnabled(True)
        self.progress_bar.setVisible(False)
        
        # 显示结果
        result_count = len(filtered_lines)
        self.result_info.setText(f"筛选完成：从 {total_lines} 行中找到 {result_count} 行匹配结果")
        
        if filtered_lines:
            self.result_text.setText('\n'.join(filtered_lines))
            self.save_btn.setEnabled(True)
        else:
            self.result_text.setText("未找到匹配的行")
            self.save_btn.setEnabled(False)
    
    def filter_error(self, error_msg):
        """筛选出错"""
        self.filter_btn.setEnabled(True)
        self.progress_bar.setVisible(False)
        QMessageBox.critical(self, "错误", f"筛选过程中出现错误：\n{error_msg}")
    
    def clear_results(self):
        """清空结果"""
        self.result_text.clear()
        self.result_info.setText("等待筛选...")
        self.save_btn.setEnabled(False)
    
    def save_results(self):
        """保存筛选结果"""
        if not self.result_text.toPlainText():
            QMessageBox.warning(self, "警告", "没有可保存的结果！")
            return
        
        file_path, _ = QFileDialog.getSaveFileName(
            self,
            "保存筛选结果",
            "筛选结果.txt",
            "文本文件 (*.txt);;所有文件 (*.*)"
        )
        
        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as file:
                    file.write(self.result_text.toPlainText())
                QMessageBox.information(self, "成功", f"结果已保存到：\n{file_path}")
            except Exception as e:
                QMessageBox.critical(self, "错误", f"保存文件时出现错误：\n{str(e)}")


def main():
    app = QApplication(sys.argv)
    
    # 设置应用程序信息
    app.setApplicationName("文本筛选工具")
    app.setApplicationVersion("1.0")
    
    # 创建并显示主窗口
    window = TextFilterApp()
    window.show()
    
    sys.exit(app.exec_())


if __name__ == '__main__':
    main()
