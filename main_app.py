import sys
import os
import time
import threading
import importlib.util
from PyQt6.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                            QHBoxLayout, QPushButton, QLabel, QTextEdit, 
                            QLineEdit, QSpinBox, QProgressBar, QFileDialog,
                            QTableWidget, QTableWidgetItem, QHeaderView,
                            QTabWidget, QGridLayout, QComboBox, QFrame,
                            QScrollArea, QGroupBox, QSplitter, QMessageBox)
from PyQt6.QtCore import QThread, pyqtSignal, Qt, QTimer
from PyQt6.QtGui import QFont, QIcon

class SchoolWorkerThread(QThread):
    """学校脚本工作线程"""
    update_signal = pyqtSignal(str)
    finished_signal = pyqtSignal(str)
    progress_signal = pyqtSignal(int)
    
    def __init__(self, script_name, driver_path, account_file, url=""):
        super().__init__()
        self.script_name = script_name
        self.driver_path = driver_path
        self.account_file = account_file
        self.url = url
        self.is_running = False
    
    def run(self):
        self.is_running = True
        try:
            # 动态导入对应的学校脚本
            script_path = f"{self.script_name}.py"
            if os.path.exists(script_path):
                spec = importlib.util.spec_from_file_location(self.script_name, script_path)
                module = importlib.util.module_from_spec(spec)
                spec.loader.exec_module(module)
                
                # 执行脚本的主要逻辑
                self.update_signal.emit(f"开始执行 {self.script_name} 脚本...")
                
                # 这里需要根据每个脚本的具体实现来调用
                if hasattr(module, 'Browser') and hasattr(module, 'read_file'):
                    browser = module.Browser(self.driver_path)
                    browser.open_browser()
                    
                    if self.url:
                        browser.open_page(self.url)
                    
                    # 读取账号文件
                    lines = module.read_file(self.account_file)
                    total = len(lines)
                    
                    for i, line in enumerate(lines):
                        if not self.is_running:
                            break
                            
                        if ":" in line:
                            username, password = line.strip().split(":")
                            try:
                                browser.register(username, password)
                                time.sleep(1)
                                self.update_signal.emit(f"处理账号: {username}")
                                self.progress_signal.emit(int((i + 1) * 100 / total))
                            except Exception as e:
                                self.update_signal.emit(f"账号 {username} 出错: {str(e)}")
                    
                    browser.close_browser()
                    
                self.finished_signal.emit(f"{self.script_name} 执行完成")
            else:
                self.update_signal.emit(f"脚本文件 {script_path} 不存在")
                
        except Exception as e:
            self.update_signal.emit(f"执行 {self.script_name} 时出错: {str(e)}")
    
    def stop(self):
        self.is_running = False

class UtilityTab(QWidget):
    """工具选项卡"""
    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent_window = parent
        self.init_ui()
    
    def init_ui(self):
        layout = QVBoxLayout(self)
        
        # 标题
        title = QLabel("数据处理工具")
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        layout.addWidget(title)
        
        # 按钮网格
        button_frame = QFrame()
        button_layout = QGridLayout(button_frame)
        
        # 工具按钮
        tools = [
            ("国家提取", self.country_extraction),
            ("删除apply等", self.remove_apply),
            ("后缀提取", self.suffix_extraction),
            ("冒号替换", self.colon_replacement),
            ("删除链接", self.remove_links),
            ("去重处理", self.remove_duplicates),
            ("URL清理", self.url_cleaner),
            ("内容合并", self.content_merge)
        ]
        
        row, col = 0, 0
        for tool_name, func in tools:
            btn = QPushButton(tool_name)
            btn.clicked.connect(func)
            btn.setMinimumHeight(40)
            button_layout.addWidget(btn, row, col)
            col += 1
            if col > 3:
                col = 0
                row += 1
        
        layout.addWidget(button_frame)
        
        # 文件选择区域
        file_group = QGroupBox("文件选择")
        file_layout = QVBoxLayout(file_group)
        
        file_select_layout = QHBoxLayout()
        self.file_label = QLabel("未选择文件")
        self.file_select_btn = QPushButton("选择文件")
        self.file_select_btn.clicked.connect(self.select_files)
        file_select_layout.addWidget(self.file_label)
        file_select_layout.addWidget(self.file_select_btn)
        file_layout.addLayout(file_select_layout)
        
        layout.addWidget(file_group)
        
        # 进度条
        self.progress_bar = QProgressBar()
        layout.addWidget(self.progress_bar)
        
        # 日志区域
        log_group = QGroupBox("处理日志")
        log_layout = QVBoxLayout(log_group)
        self.log_area = QTextEdit()
        self.log_area.setReadOnly(True)
        log_layout.addWidget(self.log_area)
        layout.addWidget(log_group)
        
        self.selected_files = []
    
    def select_files(self):
        """选择文件"""
        files, _ = QFileDialog.getOpenFileNames(self, "选择文件", "", "Text Files (*.txt);;All Files (*)")
        if files:
            self.selected_files = files
            self.file_label.setText(f"已选择 {len(files)} 个文件")
            self.log_message(f"选择了 {len(files)} 个文件")
    
    def log_message(self, message):
        """添加日志消息"""
        self.log_area.append(f"[{time.strftime('%H:%M:%S')}] {message}")
    
    def country_extraction(self):
        """国家提取"""
        if hasattr(self.parent_window, 'edu_processor'):
            self.parent_window.edu_processor.process_country_extraction()
        else:
            self.log_message("请先启动EDU数据处理器")
    
    def remove_apply(self):
        """删除apply等"""
        if hasattr(self.parent_window, 'edu_processor'):
            self.parent_window.edu_processor.process_remove_apply()
        else:
            self.log_message("请先启动EDU数据处理器")
    
    def suffix_extraction(self):
        """后缀提取"""
        if hasattr(self.parent_window, 'edu_processor'):
            self.parent_window.edu_processor.process_suffix_extraction()
        else:
            self.log_message("请先启动EDU数据处理器")
    
    def colon_replacement(self):
        """冒号替换"""
        if hasattr(self.parent_window, 'edu_processor'):
            self.parent_window.edu_processor.process_colon_replacement()
        else:
            self.log_message("请先启动EDU数据处理器")
    
    def remove_links(self):
        """删除链接"""
        if hasattr(self.parent_window, 'edu_processor'):
            self.parent_window.edu_processor.process_remove_links()
        else:
            self.log_message("请先启动EDU数据处理器")
    
    def remove_duplicates(self):
        """去重处理"""
        if hasattr(self.parent_window, 'edu_processor'):
            self.parent_window.edu_processor.process_remove_duplicates()
        else:
            self.log_message("请先启动EDU数据处理器")
    
    def url_cleaner(self):
        """URL清理"""
        self.log_message("执行URL清理...")
        # 这里可以调用url_cleaner.py的功能
    
    def content_merge(self):
        """内容合并"""
        self.log_message("执行内容合并...")
        # 这里可以调用内容合并.py的功能

class SchoolTab(QWidget):
    """学校脚本选项卡"""
    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent_window = parent
        self.worker_threads = {}
        self.init_ui()
    
    def init_ui(self):
        layout = QVBoxLayout(self)
        
        # 标题
        title = QLabel("教育机构自动化脚本")
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        layout.addWidget(title)
        
        # 设置区域
        settings_group = QGroupBox("设置")
        settings_layout = QGridLayout(settings_group)
        
        # ChromeDriver路径
        settings_layout.addWidget(QLabel("ChromeDriver路径:"), 0, 0)
        self.driver_path_edit = QLineEdit()
        self.driver_path_edit.setText(r"D:\Chrome下载\tg下载资源\check\自动化\chromedriver.exe")
        settings_layout.addWidget(self.driver_path_edit, 0, 1)
        driver_browse_btn = QPushButton("浏览")
        driver_browse_btn.clicked.connect(self.browse_driver)
        settings_layout.addWidget(driver_browse_btn, 0, 2)
        
        # 账号文件路径
        settings_layout.addWidget(QLabel("账号文件:"), 1, 0)
        self.account_file_edit = QLineEdit()
        settings_layout.addWidget(self.account_file_edit, 1, 1)
        account_browse_btn = QPushButton("浏览")
        account_browse_btn.clicked.connect(self.browse_account_file)
        settings_layout.addWidget(account_browse_btn, 1, 2)
        
        # 线程数
        settings_layout.addWidget(QLabel("并发线程数:"), 2, 0)
        self.thread_count_spin = QSpinBox()
        self.thread_count_spin.setMinimum(1)
        self.thread_count_spin.setMaximum(10)
        self.thread_count_spin.setValue(3)
        settings_layout.addWidget(self.thread_count_spin, 2, 1)
        
        layout.addWidget(settings_group)
        
        # 学校脚本选择区域
        schools_group = QGroupBox("教育机构选择")
        schools_layout = QVBoxLayout(schools_group)
        
        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_widget = QWidget()
        scroll_layout = QGridLayout(scroll_widget)
        
        # 获取所有学校脚本
        self.schools = self.get_school_scripts()
        self.school_buttons = {}
        
        row, col = 0, 0
        for school in self.schools:
            btn = QPushButton(school.replace('.py', '').upper())
            btn.setCheckable(True)
            btn.setMinimumHeight(35)
            btn.clicked.connect(lambda checked, s=school: self.toggle_school(s, checked))
            self.school_buttons[school] = btn
            scroll_layout.addWidget(btn, row, col)
            col += 1
            if col > 4:
                col = 0
                row += 1
        
        scroll_area.setWidget(scroll_widget)
        scroll_area.setWidgetResizable(True)
        schools_layout.addWidget(scroll_area)
        
        # 控制按钮
        control_layout = QHBoxLayout()
        self.start_btn = QPushButton("开始执行")
        self.start_btn.clicked.connect(self.start_execution)
        self.stop_btn = QPushButton("停止执行")
        self.stop_btn.clicked.connect(self.stop_execution)
        self.stop_btn.setEnabled(False)
        
        select_all_btn = QPushButton("全选")
        select_all_btn.clicked.connect(self.select_all_schools)
        deselect_all_btn = QPushButton("全不选")
        deselect_all_btn.clicked.connect(self.deselect_all_schools)
        
        control_layout.addWidget(select_all_btn)
        control_layout.addWidget(deselect_all_btn)
        control_layout.addStretch()
        control_layout.addWidget(self.start_btn)
        control_layout.addWidget(self.stop_btn)
        
        schools_layout.addLayout(control_layout)
        layout.addWidget(schools_group)
        
        # 进度和状态
        progress_group = QGroupBox("执行状态")
        progress_layout = QVBoxLayout(progress_group)
        
        self.progress_bar = QProgressBar()
        progress_layout.addWidget(self.progress_bar)
        
        self.status_table = QTableWidget()
        self.status_table.setColumnCount(4)
        self.status_table.setHorizontalHeaderLabels(["学校", "状态", "进度", "消息"])
        self.status_table.horizontalHeader().setStretchLastSection(True)
        progress_layout.addWidget(self.status_table)
        
        layout.addWidget(progress_group)
        
        # 日志区域
        log_group = QGroupBox("执行日志")
        log_layout = QVBoxLayout(log_group)
        self.log_area = QTextEdit()
        self.log_area.setReadOnly(True)
        log_layout.addWidget(self.log_area)
        layout.addWidget(log_group)
        
        self.selected_schools = set()
    
    def get_school_scripts(self):
        """获取所有学校脚本"""
        scripts = []
        for file in os.listdir("."):
            if file.endswith(".py") and file not in ["main_app.py", "第1步edu脚本.py", "fju_pyqt.py", "依次执行.py"]:
                # 排除一些非学校脚本
                exclude_patterns = ["删除", "清理", "合并", "提取", "url_", "oracle", "outlook", "lexical", "access"]
                if not any(pattern in file for pattern in exclude_patterns):
                    scripts.append(file)
        return sorted(scripts)
    
    def browse_driver(self):
        """浏览ChromeDriver"""
        file_path, _ = QFileDialog.getOpenFileName(self, "选择ChromeDriver", "", "Executable Files (*.exe);;All Files (*)")
        if file_path:
            self.driver_path_edit.setText(file_path)
    
    def browse_account_file(self):
        """浏览账号文件"""
        file_path, _ = QFileDialog.getOpenFileName(self, "选择账号文件", "", "Text Files (*.txt);;All Files (*)")
        if file_path:
            self.account_file_edit.setText(file_path)
    
    def toggle_school(self, school, checked):
        """切换学校选择状态"""
        if checked:
            self.selected_schools.add(school)
        else:
            self.selected_schools.discard(school)
        
        self.log_message(f"{'选择' if checked else '取消选择'} {school}")
    
    def select_all_schools(self):
        """全选学校"""
        for school, btn in self.school_buttons.items():
            btn.setChecked(True)
            self.selected_schools.add(school)
        self.log_message("已选择所有学校脚本")
    
    def deselect_all_schools(self):
        """取消全选"""
        for school, btn in self.school_buttons.items():
            btn.setChecked(False)
        self.selected_schools.clear()
        self.log_message("已取消选择所有学校脚本")
    
    def start_execution(self):
        """开始执行"""
        if not self.selected_schools:
            QMessageBox.warning(self, "警告", "请至少选择一个学校脚本")
            return
        
        if not self.driver_path_edit.text() or not self.account_file_edit.text():
            QMessageBox.warning(self, "警告", "请设置ChromeDriver路径和账号文件")
            return
        
        self.start_btn.setEnabled(False)
        self.stop_btn.setEnabled(True)
        
        # 清空状态表格
        self.status_table.setRowCount(len(self.selected_schools))
        
        # 启动工作线程
        for i, school in enumerate(self.selected_schools):
            # 添加到状态表格
            self.status_table.setItem(i, 0, QTableWidgetItem(school))
            self.status_table.setItem(i, 1, QTableWidgetItem("准备中"))
            self.status_table.setItem(i, 2, QTableWidgetItem("0%"))
            self.status_table.setItem(i, 3, QTableWidgetItem("等待开始"))
            
            # 创建工作线程
            worker = SchoolWorkerThread(
                school.replace('.py', ''),
                self.driver_path_edit.text(),
                self.account_file_edit.text()
            )
            worker.update_signal.connect(lambda msg, s=school: self.update_school_status(s, msg))
            worker.finished_signal.connect(lambda msg, s=school: self.school_finished(s, msg))
            worker.progress_signal.connect(lambda prog, s=school: self.update_school_progress(s, prog))
            
            self.worker_threads[school] = worker
            worker.start()
        
        self.log_message(f"开始执行 {len(self.selected_schools)} 个学校脚本")
    
    def stop_execution(self):
        """停止执行"""
        for worker in self.worker_threads.values():
            worker.stop()
            worker.wait()
        
        self.worker_threads.clear()
        self.start_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
        self.log_message("已停止所有脚本执行")
    
    def update_school_status(self, school, message):
        """更新学校状态"""
        for row in range(self.status_table.rowCount()):
            if self.status_table.item(row, 0).text() == school:
                self.status_table.setItem(row, 1, QTableWidgetItem("执行中"))
                self.status_table.setItem(row, 3, QTableWidgetItem(message))
                break
        self.log_message(f"[{school}] {message}")
    
    def update_school_progress(self, school, progress):
        """更新学校进度"""
        for row in range(self.status_table.rowCount()):
            if self.status_table.item(row, 0).text() == school:
                self.status_table.setItem(row, 2, QTableWidgetItem(f"{progress}%"))
                break
    
    def school_finished(self, school, message):
        """学校脚本完成"""
        for row in range(self.status_table.rowCount()):
            if self.status_table.item(row, 0).text() == school:
                self.status_table.setItem(row, 1, QTableWidgetItem("已完成"))
                self.status_table.setItem(row, 3, QTableWidgetItem(message))
                break
        
        # 检查是否所有脚本都完成
        if school in self.worker_threads:
            del self.worker_threads[school]
        
        if not self.worker_threads:
            self.start_btn.setEnabled(True)
            self.stop_btn.setEnabled(False)
            self.log_message("所有脚本执行完成")
    
    def log_message(self, message):
        """添加日志消息"""
        self.log_area.append(f"[{time.strftime('%H:%M:%S')}] {message}")

class MainWindow(QMainWindow):
    """主窗口"""
    def __init__(self):
        super().__init__()
        self.init_ui()
        self.init_edu_processor()
    
    def init_ui(self):
        self.setWindowTitle("教育机构自动化工具集成平台")
        self.setGeometry(100, 100, 1200, 800)
        
        # 中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        layout = QVBoxLayout(central_widget)
        
        # 标题
        title = QLabel("教育机构自动化工具集成平台")
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title.setFont(QFont("Arial", 20, QFont.Weight.Bold))
        layout.addWidget(title)
        
        # 创建选项卡
        self.tab_widget = QTabWidget()
        
        # 学校脚本选项卡
        self.school_tab = SchoolTab(self)
        self.tab_widget.addTab(self.school_tab, "学校脚本")
        
        # 数据处理工具选项卡
        self.utility_tab = UtilityTab(self)
        self.tab_widget.addTab(self.utility_tab, "数据处理工具")
        
        layout.addWidget(self.tab_widget)
        
        # 状态栏
        self.statusBar().showMessage("就绪")
    
    def init_edu_processor(self):
        """初始化EDU数据处理器"""
        try:
            # 动态导入EDU脚本
            if os.path.exists("第1步edu脚本.py"):
                spec = importlib.util.spec_from_file_location("edu_script", "第1步edu脚本.py")
                edu_module = importlib.util.module_from_spec(spec)
                spec.loader.exec_module(edu_module)
                
                if hasattr(edu_module, 'EduDataProcessor'):
                    self.edu_processor = edu_module.EduDataProcessor()
                    self.utility_tab.log_message("EDU数据处理器初始化完成")
                else:
                    self.utility_tab.log_message("未找到EduDataProcessor类")
            else:
                self.utility_tab.log_message("未找到第1步edu脚本.py文件")
        except Exception as e:
            self.utility_tab.log_message(f"初始化EDU数据处理器失败: {str(e)}")

def main():
    app = QApplication(sys.argv)
    
    # 设置应用程序样式
    app.setStyle('Fusion')
    
    # 创建主窗口
    window = MainWindow()
    window.show()
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main() 