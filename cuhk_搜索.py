import re

def search_cuhk_links(input_file, output_file=None):
    """
    从指定文件中搜索包含 'sts.cuhk.edu.hk' 的链接
    
    Args:
        input_file (str): 输入文件路径
        output_file (str): 输出文件路径，如果为None则只打印到控制台
    """
    found_links = []
    
    try:
        with open(input_file, 'r', encoding='utf-8') as file:
            for line_num, line in enumerate(file, 1):
                # 检查行中是否包含 'sts.cuhk.edu.hk'
                if 'sts.cuhk.edu.hk' in line:
                    found_links.append((line_num, line.strip()))
        
        # 打印结果
        print(f"在文件 '{input_file}' 中找到 {len(found_links)} 个包含 'sts.cuhk.edu.hk' 的链接:")
        print("-" * 80)
        
        for line_num, link in found_links:
            print(f"第 {line_num} 行: {link}")
        
        # 如果指定了输出文件，则保存结果
        if output_file:
            with open(output_file, 'w', encoding='utf-8') as out_file:
                for line_num, link in found_links:
                    out_file.write(f"{link}\n")

        
    except FileNotFoundError:
        print(f"错误: 找不到文件 '{input_file}'")
        return []
    except Exception as e:
        print(f"读取文件时发生错误: {e}")
        return []

if __name__ == "__main__":
    # 搜索包含 'sts.cuhk.edu.hk' 的链接
    input_filename = "hk.txt"
    output_filename = "cuhk_links_result.txt"
    
    print("正在搜索包含 'sts.cuhk.edu.hk' 的链接...")
    found_links = search_cuhk_links(input_filename, output_filename)
    
    if not found_links:
        print("未找到包含 'sts.cuhk.edu.hk' 的链接。") 