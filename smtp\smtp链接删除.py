import os

# 定义处理文件的函数
def process_file(file_path):
    if not os.path.exists(file_path):
        print(f"文件 '{file_path}' 不存在，跳过。")
        return
    
    with open(file_path, 'r', encoding='utf-8') as file:
        lines = file.readlines()
    
    processed_lines = []
    for line in lines:
        last_colon_index = line.rfind(':')
        second_last_colon_index = line.rfind(':', 0, last_colon_index)
        
        if second_last_colon_index != -1:
            processed_line = line[second_last_colon_index + 1:]
        else:
            processed_line = line
        
        processed_lines.append(processed_line)
    
    with open(file_path, 'w', encoding='utf-8') as file:
        file.writelines(processed_lines)

    print(f"文件 '{file_path}' 处理完成。")

# 获取用户输入的文件名
file_path = input("请输入要删除链接的文件: ")

# 处理用户输入的文件
process_file(file_path)

print("输出已完成。")
