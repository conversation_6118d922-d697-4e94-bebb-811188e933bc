# 让用户输入要处理的文件名
input_filename = input("请输入要修改格式的文件名: ")

# 让用户输入保存结果的文件名
output_filename = input("请输入保存到的文件名: ")

try:
    # 打开输入文件并读取数据
    with open(input_filename, 'r', encoding="utf-8") as file:
        lines = file.readlines()

    # 打开输出文件用于写入
    with open(output_filename, 'w', encoding="utf-8") as output_file:
        # 遍历每一行，提取电子邮件和密码，并将其转换为所需的格式
        for line_number, line in enumerate(lines, start=1):
            try:
                # 假设每一行是以 '|' 分隔的电子邮件和密码
                email, password = line.strip().split('|')
                output = f"smtp.office365.com|587|{email}|{password}\n"
                output_file.write(output)
            except ValueError:
                print(f"文件格式不正确。请确保每一行包含 '|' 分隔的电子邮件和密码。出错行号: {line_number}")
                break

    print(f"数据已成功写入到 {output_filename} 文件中。")

except UnicodeDecodeError:
    print("文件编码不正确，无法解码。请检查文件的编码格式，并尝试使用 UTF-8 或 GBK 编码。")
except FileNotFoundError:
    print(f"文件 '{input_filename}' 未找到。请检查文件名并重试。")
except Exception as e:
    print(f"发生错误：{e}")
