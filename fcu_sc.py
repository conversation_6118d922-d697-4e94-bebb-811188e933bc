# import re

# # 读取文件内容
# with open("nau.txt", "r", encoding="utf-8") as file:
#     content = file.readlines()

# # 定义关键字列表
# keywords = [
#     'cas.nau.edu',
#     'id.nau.edu',
# ]

# # 删除包含任何关键字的行
# cleaned_content = [line for line in content if not any(keyword in line for keyword in keywords)]

# # 将清理后的内容写回文件
# with open("nau.txt", "w", encoding="utf-8") as file:
#     file.writelines(cleaned_content)

# print("清理完成，结果已保存到 nau.txt")

import re

# 读取文件内容
with open("fcu.txt", "r", encoding="utf-8") as file:
    content = file.readlines()

# 使用正则表达式删除每行的 "Line xxxxxx:" 部分
pattern = r'Line \d+: '

# 对每一行应用正则替换
cleaned_content = [re.sub(pattern, '', line) for line in content]

# 删除空行
cleaned_content = [line for line in content if not line.strip() == ""]

# 使用正则表达式删除每行的 "File: " 部分
pattern = r'File:\s*".*?\.txt"'
cleaned_content = [re.sub(pattern, '', line) for line in cleaned_content]

# 将清理后的内容写回文件
with open("fcu.txt", "w", encoding="utf-8") as file:
    file.writelines(cleaned_content)


# 删除包含下列关键字的行
keywords = [
    '@alumni.fcu.edu.tw',
    'gmail.com',
    'GMAIL.COM',
]
cleaned_content = [line for line in cleaned_content if not any(keyword in line for keyword in keywords)]


print("清理完成，结果已保存到 fcu.txt")

