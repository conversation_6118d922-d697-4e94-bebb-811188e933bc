import sys
import os
import re
from PyQt6.QtWidgets import (QApplication, QMainWindow, QPushButton, QVBoxLayout, 
                           QWidget, QFileDialog, QLabel, QTextEdit, QProgressBar, 
                           QHBoxLayout, QLineEdit, QGridLayout, QGroupBox)
from PyQt6.QtCore import Qt

class EduLinkClassifier(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle('教育机构链接分类器')
        self.setGeometry(100, 100, 950, 750)
        
        # 创建主窗口部件
        main_widget = QWidget()
        self.setCentralWidget(main_widget)
        
        # 创建垂直布局
        layout = QVBoxLayout()
        
        # 创建导出设置组
        export_group = QGroupBox("导出设置")
        export_layout = QGridLayout()
        
        # 导出文件夹名称
        export_layout.addWidget(QLabel("输出文件夹名称:"), 0, 0)
        self.folder_name_input = QLineEdit()
        self.folder_name_input.setText("edu_links")  # 默认值
        self.folder_name_input.setPlaceholderText("请输入文件夹名称（如：edu_links、学校链接等）")
        export_layout.addWidget(self.folder_name_input, 0, 1)
        
        # 导出路径选择
        export_layout.addWidget(QLabel("导出基础路径:"), 1, 0)
        path_layout = QHBoxLayout()
        self.export_path_input = QLineEdit()
        self.export_path_input.setPlaceholderText("留空则使用源文件同目录")
        path_layout.addWidget(self.export_path_input)
        
        self.select_path_btn = QPushButton('选择路径')
        self.select_path_btn.clicked.connect(self.select_export_path)
        path_layout.addWidget(self.select_path_btn)
        
        export_layout.addLayout(path_layout, 1, 1)
        
        # 域名后缀设置
        export_layout.addWidget(QLabel("目标域名后缀:"), 2, 0)
        self.domain_suffix_input = QLineEdit()
        self.domain_suffix_input.setText(".edu,.ac.uk,.edu.au,.ac.jp,.edu.cn,.ac.kr,.edu.au")  # 默认值
        self.domain_suffix_input.setPlaceholderText("请输入域名后缀，用逗号分隔（如：.edu,.ac.uk,.edu.au）")
        export_layout.addWidget(self.domain_suffix_input, 2, 1)
        
        export_group.setLayout(export_layout)
        layout.addWidget(export_group)
        
        # 创建按钮布局
        button_layout = QHBoxLayout()
        
        # 添加选择文件按钮
        self.select_btn = QPushButton('选择文件')
        self.select_btn.clicked.connect(self.select_files)
        button_layout.addWidget(self.select_btn)
        
        # 添加处理特殊链接按钮
        self.special_btn = QPushButton('处理特殊链接')
        self.special_btn.clicked.connect(self.select_special_files)
        button_layout.addWidget(self.special_btn)
        
        # 添加处理冒号分隔链接按钮
        self.colon_btn = QPushButton('处理冒号链接')
        self.colon_btn.clicked.connect(self.select_colon_files)
        button_layout.addWidget(self.colon_btn)
        
        # 将按钮布局添加到主布局
        layout.addLayout(button_layout)
        
        # 添加状态标签
        self.status_label = QLabel('请选择要处理的文件')
        layout.addWidget(self.status_label)
        
        # 添加进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        # 添加日志显示区域
        self.log_area = QTextEdit()
        self.log_area.setReadOnly(True)
        layout.addWidget(self.log_area)
        
        # 设置布局
        main_widget.setLayout(layout)
    
    def get_domain_suffixes(self):
        """获取用户设置的域名后缀列表"""
        suffixes_text = self.domain_suffix_input.text().strip()
        if not suffixes_text:
            return ['.edu']  # 默认后缀
        
        # 分割并清理后缀
        suffixes = []
        for suffix in suffixes_text.split(','):
            suffix = suffix.strip()
            if suffix and not suffix.startswith('.'):
                suffix = '.' + suffix
            if suffix:
                suffixes.append(suffix.lower())
        
        return suffixes if suffixes else ['.edu']
    
    def select_export_path(self):
        """选择导出基础路径"""
        folder = QFileDialog.getExistingDirectory(
            self,
            "选择导出基础路径",
            ""
        )
        if folder:
            self.export_path_input.setText(folder)
            self.log_message(f"已设置导出路径: {folder}")
    
    def get_output_directory(self, source_file_path):
        """获取输出目录路径"""
        # 获取文件夹名称
        folder_name = self.folder_name_input.text().strip()
        if not folder_name:
            folder_name = "edu_links"  # 默认名称
        
        # 获取基础路径
        base_path = self.export_path_input.text().strip()
        if not base_path:
            # 如果没有指定基础路径，使用源文件的目录
            base_path = os.path.dirname(source_file_path)
        
        # 组合完整的输出目录路径
        output_dir = os.path.join(base_path, folder_name)
        return output_dir

    def log_message(self, message):
        """添加日志消息"""
        self.log_area.append(f"[{message}]")
        # 确保日志区域滚动到最新内容
        self.log_area.verticalScrollBar().setValue(
            self.log_area.verticalScrollBar().maximum()
        )
        # 让 Qt 处理待处理的事件，以便实时更新界面
        QApplication.processEvents()

    def extract_main_domain(self, url):
        try:
            # 获取用户设置的域名后缀
            target_suffixes = self.get_domain_suffixes()
            
            # 检查是否包含任何目标域名后缀
            url_lower = url.lower()
            matching_suffix = None
            for suffix in target_suffixes:
                if suffix in url_lower:
                    matching_suffix = suffix
                    break
            
            if not matching_suffix:
                return None
                
            # 处理URL格式
            # 如果URL包含协议，确保我们正确处理
            if '://' in url:
                # 提取协议后面的部分
                url_parts = url.split('://', 1)
                if len(url_parts) > 1:
                    domain_part = url_parts[1].split('/', 1)[0]
                else:
                    domain_part = url
            else:
                # 没有协议的情况下，尝试获取域名部分
                domain_part = url.split('/', 1)[0] if '/' in url else url
            
            # 移除可能的端口号
            if ':' in domain_part:
                domain_part = domain_part.split(':', 1)[0]
                
            # 检查域名是否包含目标后缀
            domain_part_lower = domain_part.lower()
            if matching_suffix not in domain_part_lower:
                # 尝试查找URL其他部分是否包含目标域名格式
                for suffix in target_suffixes:
                    # 创建匹配模式，例如：([a-zA-Z0-9_-]+)\.edu 或 ([a-zA-Z0-9_-]+)\.ac\.uk
                    escaped_suffix = re.escape(suffix[1:])  # 移除开头的点并转义
                    pattern = rf'([a-zA-Z0-9_-]+)\.{escaped_suffix}'
                    match = re.search(pattern, url_lower)
                    if match:
                        return match.group(1)
                return None
                
            # 提取主域名
            # 对于复合后缀如.ac.uk，需要特殊处理
            parts = domain_part.split('.')
            
            # 查找匹配的后缀在域名中的位置
            suffix_parts = matching_suffix[1:].split('.')  # 移除开头的点并分割
            
            # 从后往前匹配后缀
            if len(parts) >= len(suffix_parts) + 1:  # 至少要有主域名+后缀
                # 检查后缀是否匹配
                suffix_match = True
                for i, suffix_part in enumerate(reversed(suffix_parts)):
                    if len(parts) > i and parts[-(i+1)].lower() != suffix_part.lower():
                        suffix_match = False
                        break
                
                if suffix_match:
                    # 提取主域名（后缀前面的部分）
                    main_domain_index = len(parts) - len(suffix_parts) - 1
                    if main_domain_index >= 0:
                        main_domain = parts[main_domain_index]
                        # 清理域名中的特殊字符
                        main_domain = re.sub(r'[^a-zA-Z0-9_-]', '', main_domain)
                        return main_domain if main_domain else None
            
            return None
            
        except Exception as e:
            self.log_message(f"处理域名时出错: {str(e)} - URL: {url}")
            return None
        
    def select_files(self):
        files, _ = QFileDialog.getOpenFileNames(
            self,
            "选择文件",
            "",
            "文本文件 (*.txt)"
        )
        
        if files:
            self.log_message(f"选择了 {len(files)} 个文件待处理")
            self.log_message(f"目标域名后缀: {', '.join(self.get_domain_suffixes())}")
            self.process_files(files)
    
    def process_files(self, files):
        self.log_area.clear()
        self.status_label.setText('正在处理文件...')
        self.progress_bar.setVisible(True)
        self.progress_bar.setMaximum(len(files))
        self.progress_bar.setValue(0)
        
        total_links = 0
        total_domains = set()
        target_suffixes = self.get_domain_suffixes()
        
        for index, file_path in enumerate(files, 1):
            try:
                self.log_message(f"开始处理文件: {os.path.basename(file_path)}")
                
                # 创建输出目录（使用自定义路径和名称）
                output_dir = self.get_output_directory(file_path)
                os.makedirs(output_dir, exist_ok=True)
                self.log_message(f"输出目录: {output_dir}")
                
                # 读取文件内容
                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    lines = f.readlines()
                self.log_message(f"文件共 {len(lines)} 行")
                
                # 分类链接
                domains = {}
                links_found = 0
                checked_lines = 0
                
                for line_num, line in enumerate(lines, 1):
                    line = line.strip()
                    checked_lines += 1
                    
                    # 检查整行是否包含任何目标域名后缀
                    line_lower = line.lower()
                    contains_target_suffix = any(suffix in line_lower for suffix in target_suffixes)
                    
                    if contains_target_suffix:
                        # 首先尝试将整行作为一个URL处理
                        main_domain = self.extract_main_domain(line)
                        if main_domain:
                            if main_domain not in domains:
                                domains[main_domain] = []
                            domains[main_domain].append(line)
                            links_found += 1
                        else:
                            # 如果整行不是一个有效的目标URL，尝试提取包含目标后缀的URL部分
                            for suffix in target_suffixes:
                                # 创建URL匹配模式
                                escaped_suffix = re.escape(suffix)
                                url_pattern = rf'https?://[^\s()<>]+{escaped_suffix}[^\s()<>]*'
                                urls = re.findall(url_pattern, line, re.IGNORECASE)
                                
                                # 也匹配不带协议的URL
                                no_protocol_pattern = rf'[a-zA-Z0-9-]+\.[a-zA-Z0-9.-]*{escaped_suffix}[^\s()<>]*'
                                urls.extend(re.findall(no_protocol_pattern, line, re.IGNORECASE))
                                
                                for url in urls:
                                    domain = self.extract_main_domain(url)
                                    if domain:
                                        if domain not in domains:
                                            domains[domain] = []
                                        domains[domain].append(url)
                                        links_found += 1
                
                # 显示处理进度信息
                if checked_lines % 100 == 0 or checked_lines == len(lines):
                    self.log_message(f"已检查 {checked_lines}/{len(lines)} 行，找到 {links_found} 个目标链接")
                
                # 保存分类结果
                file_total_links = 0
                for domain, links in domains.items():
                    output_file = os.path.join(output_dir, f'{domain}.txt')
                    
                    with open(output_file, 'w', encoding='utf-8') as f:
                        for link in links:
                            f.write(f'{link}\n')
                    
                    file_total_links += len(links)
                    total_domains.add(domain)
                    self.log_message(f'域名 {domain} - 已保存 {len(links)} 个链接到 {os.path.basename(output_file)}')
                
                total_links += file_total_links
                self.log_message(f"文件处理完成: 找到 {file_total_links} 个目标链接\n")
                
                # 更新进度条
                self.progress_bar.setValue(index)
                
            except Exception as e:
                self.log_message(f'处理文件出错: {str(e)}')
                import traceback
                self.log_message(traceback.format_exc())
                self.status_label.setText('处理过程中出现错误')
        
        # 显示最终统计信息
        self.log_message(f"\n处理完成！总计:")
        self.log_message(f"- 处理文件数: {len(files)}")
        self.log_message(f"- 发现域名数: {len(total_domains)}")
        self.log_message(f"- 总链接数: {total_links}")
        self.log_message(f"- 目标后缀: {', '.join(target_suffixes)}")
        self.log_message(f"- 导出位置: {self.get_output_directory(files[0]) if files else '未知'}")
        
        self.status_label.setText('处理完成！')

    def select_special_files(self):
        files, _ = QFileDialog.getOpenFileNames(
            self,
            "选择包含特殊链接的文件",
            "",
            "文本文件 (*.txt)"
        )
        
        if files:
            self.log_message(f"选择了 {len(files)} 个文件待特殊处理")
            self.log_message(f"目标域名后缀: {', '.join(self.get_domain_suffixes())}")
            self.process_special_files(files)
    
    def process_special_files(self, files):
        self.log_area.clear()
        self.status_label.setText('正在处理特殊链接文件...')
        self.progress_bar.setVisible(True)
        self.progress_bar.setMaximum(len(files))
        self.progress_bar.setValue(0)
        
        total_links = 0
        total_domains = set()
        target_suffixes = self.get_domain_suffixes()
        
        for index, file_path in enumerate(files, 1):
            try:
                self.log_message(f"开始处理文件: {os.path.basename(file_path)}")
                
                # 创建输出目录（使用自定义路径和名称）
                output_dir = self.get_output_directory(file_path)
                os.makedirs(output_dir, exist_ok=True)
                self.log_message(f"输出目录: {output_dir}")
                
                # 读取文件内容
                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    lines = f.readlines()
                self.log_message(f"文件共 {len(lines)} 行")
                
                # 分类链接
                domains = {}
                links_found = 0
                
                for line_num, line in enumerate(lines, 1):
                    line = line.strip()
                    
                    # 检查特殊格式的链接
                    line_lower = line.lower()
                    contains_target_suffix = any(suffix in line_lower for suffix in target_suffixes)
                    
                    if contains_target_suffix:
                        # 尝试匹配不同格式的目标链接
                        for suffix in target_suffixes:
                            escaped_suffix = re.escape(suffix)
                            # 1. 带协议和冒号分隔符的链接
                            special_pattern = rf'(https?://[^:\s]+{escaped_suffix}[^:\s]*):?([^/\s]*)'
                            match = re.search(special_pattern, line, re.IGNORECASE)
                            
                            if match:
                                base_url = match.group(1)  # 提取基本URL
                                # 尝试从基本URL提取域名
                                domain = self.extract_main_domain(base_url)
                                if domain:
                                    if domain not in domains:
                                        domains[domain] = []
                                    domains[domain].append(line)
                                    links_found += 1
                                    self.log_message(f"找到特殊链接: {line}")
                                    break  # 找到一个匹配就跳出循环
                        
                        # 2. 如果上面没有匹配，尝试提取任何包含目标后缀的部分
                        if not any(line in domain_links for domain_links in domains.values()):
                            for suffix in target_suffixes:
                                escaped_suffix = re.escape(suffix)
                                parts = re.findall(rf'([a-zA-Z0-9.-]+{escaped_suffix}[^/\s,;]*)', line, re.IGNORECASE)
                                for part in parts:
                                    domain = self.extract_main_domain(part)
                                    if domain:
                                        if domain not in domains:
                                            domains[domain] = []
                                        domains[domain].append(line)
                                        links_found += 1
                                        self.log_message(f"找到目标链接: {line}")
                                        break  # 一行只计算一次
                                if parts:  # 如果找到了匹配，跳出外层循环
                                    break
                
                # 保存分类结果
                file_total_links = 0
                for domain, links in domains.items():
                    output_file = os.path.join(output_dir, f'{domain}.txt')
                    
                    with open(output_file, 'w', encoding='utf-8') as f:
                        for link in links:
                            f.write(f'{link}\n')
                    
                    file_total_links += len(links)
                    total_domains.add(domain)
                    self.log_message(f'域名 {domain} - 已保存 {len(links)} 个链接到 {os.path.basename(output_file)}')
                
                total_links += file_total_links
                self.log_message(f"文件处理完成: 找到 {file_total_links} 个目标链接\n")
                
                # 更新进度条
                self.progress_bar.setValue(index)
                
            except Exception as e:
                self.log_message(f'处理文件出错: {str(e)}')
                import traceback
                self.log_message(traceback.format_exc())
                self.status_label.setText('处理过程中出现错误')
        
        # 显示最终统计信息
        self.log_message(f"\n处理完成！总计:")
        self.log_message(f"- 处理文件数: {len(files)}")
        self.log_message(f"- 发现域名数: {len(total_domains)}")
        self.log_message(f"- 总链接数: {total_links}")
        self.log_message(f"- 目标后缀: {', '.join(target_suffixes)}")
        self.log_message(f"- 导出位置: {self.get_output_directory(files[0]) if files else '未知'}")
        
        self.status_label.setText('处理完成！')

    def select_colon_files(self):
        files, _ = QFileDialog.getOpenFileNames(
            self,
            "选择包含冒号分隔链接的文件",
            "",
            "文本文件 (*.txt)"
        )
        
        if files:
            self.log_message(f"选择了 {len(files)} 个文件待冒号链接处理")
            self.log_message(f"目标域名后缀: {', '.join(self.get_domain_suffixes())}")
            self.process_colon_files(files)
    
    def process_colon_files(self, files):
        self.log_area.clear()
        self.status_label.setText('正在处理冒号分隔链接文件...')
        self.progress_bar.setVisible(True)
        self.progress_bar.setMaximum(len(files))
        self.progress_bar.setValue(0)
        
        total_links = 0
        total_domains = set()
        target_suffixes = self.get_domain_suffixes()
        
        for index, file_path in enumerate(files, 1):
            try:
                self.log_message(f"开始处理文件: {os.path.basename(file_path)}")
                
                # 创建输出目录（使用自定义路径和名称）
                output_dir = self.get_output_directory(file_path)
                os.makedirs(output_dir, exist_ok=True)
                self.log_message(f"输出目录: {output_dir}")
                
                # 读取文件内容
                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()
                
                # 为每个目标后缀创建匹配模式
                all_matches = []
                for suffix in target_suffixes:
                    escaped_suffix = re.escape(suffix)
                    pattern = rf'(https?://[^\s]+{escaped_suffix}[^\s]*)(?::([^\s]*))?'
                    matches = re.findall(pattern, content, re.IGNORECASE)
                    all_matches.extend(matches)
                
                self.log_message(f"找到 {len(all_matches)} 个可能的目标链接")
                
                # 分类链接
                domains = {}
                
                for url_part, suffix_part in all_matches:
                    # 尝试提取域名
                    full_url = f"{url_part}:{suffix_part}" if suffix_part else url_part
                    domain = None
                    
                    # 从URL中提取域名部分
                    domain_match = re.search(r'//([^/]+)', url_part)
                    if domain_match:
                        domain_part = domain_match.group(1)
                        # 检查域名是否包含任何目标后缀
                        domain_part_lower = domain_part.lower()
                        
                        for target_suffix in target_suffixes:
                            if target_suffix in domain_part_lower:
                                # 提取主域名
                                # 对于复合后缀如.ac.uk，需要特殊处理
                                parts = domain_part.split('.')
                                suffix_parts = target_suffix[1:].split('.')  # 移除开头的点并分割
                                
                                # 从后往前匹配后缀
                                if len(parts) >= len(suffix_parts) + 1:  # 至少要有主域名+后缀
                                    # 检查后缀是否匹配
                                    suffix_match = True
                                    for i, suf_part in enumerate(reversed(suffix_parts)):
                                        if len(parts) > i and parts[-(i+1)].lower() != suf_part.lower():
                                            suffix_match = False
                                            break
                                    
                                    if suffix_match:
                                        # 提取主域名（后缀前面的部分）
                                        main_domain_index = len(parts) - len(suffix_parts) - 1
                                        if main_domain_index >= 0:
                                            domain = parts[main_domain_index]
                                            # 清理域名中的特殊字符
                                            domain = re.sub(r'[^a-zA-Z0-9_-]', '', domain)
                                            break  # 找到匹配的后缀就跳出
                    
                    if domain:
                        if domain not in domains:
                            domains[domain] = []
                        domains[domain].append(full_url)
                        self.log_message(f"找到链接: {full_url} -> 域名: {domain}")
                
                # 保存分类结果
                file_total_links = 0
                for domain, links in domains.items():
                    output_file = os.path.join(output_dir, f'{domain}.txt')
                    
                    with open(output_file, 'w', encoding='utf-8') as f:
                        for link in links:
                            f.write(f'{link}\n')
                    
                    file_total_links += len(links)
                    total_domains.add(domain)
                    self.log_message(f'域名 {domain} - 已保存 {len(links)} 个链接到 {os.path.basename(output_file)}')
                
                total_links += file_total_links
                self.log_message(f"文件处理完成: 找到 {file_total_links} 个目标链接\n")
                
                # 更新进度条
                self.progress_bar.setValue(index)
                
            except Exception as e:
                self.log_message(f'处理文件出错: {str(e)}')
                import traceback
                self.log_message(traceback.format_exc())
                self.status_label.setText('处理过程中出现错误')
        
        # 显示最终统计信息
        self.log_message(f"\n处理完成！总计:")
        self.log_message(f"- 处理文件数: {len(files)}")
        self.log_message(f"- 发现域名数: {len(total_domains)}")
        self.log_message(f"- 总链接数: {total_links}")
        self.log_message(f"- 目标后缀: {', '.join(target_suffixes)}")
        self.log_message(f"- 导出位置: {self.get_output_directory(files[0]) if files else '未知'}")
        
        self.status_label.setText('处理完成！')

if __name__ == '__main__':
    app = QApplication(sys.argv)
    window = EduLinkClassifier()
    window.show()
    sys.exit(app.exec()) 