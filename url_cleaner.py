#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
URL清理工具
删除URL链接部分，只保留用户名和密码
"""

import re
import sys
import os
from PyQt6.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                           QPushButton, QTextEdit, QFileDialog, QMessageBox)
from PyQt6.QtCore import Qt

def clean_url_line(line):
    """
    清理单行URL，删除域名部分，只保留用户名和密码
    
    Args:
        line (str): 包含URL的行
        
    Returns:
        str: 清理后的行，如果格式不匹配则返回原行
    """
    # 去除行首行尾空白
    line = line.strip()
    
    # 如果是空行，直接返回
    if not line:
        return line
    
    # 匹配模式：域名/:用户名:密码
    # 支持多种格式：
    # 1. domain.com/:username:password
    # 2. domain.com/path/:username:password
    # 3. https://domain.com/:username:password
    pattern = r'^(?:https?://)?([^/]+)(?:/[^:]*)?:([^:]+:[^:]+)$'
    
    match = re.match(pattern, line)
    if match:
        # 返回用户名:密码部分
        return match.group(2)
    else:
        # 如果不匹配预期格式，返回原行
        return line

def clean_file(input_file, output_file=None):
    """
    清理文件中的所有URL
    
    Args:
        input_file (str): 输入文件路径
        output_file (str): 输出文件路径，如果为None则覆盖原文件
    """
    try:
        # 读取文件
        with open(input_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        # 清理每一行
        cleaned_lines = []
        for line in lines:
            cleaned_line = clean_url_line(line.rstrip('\n\r'))
            if cleaned_line:  # 只保留非空行
                cleaned_lines.append(cleaned_line)
        
        # 确定输出文件
        if output_file is None:
            output_file = input_file
        
        # 写入清理后的内容
        with open(output_file, 'w', encoding='utf-8') as f:
            for line in cleaned_lines:
                f.write(line + '\n')
        
        print(f"清理完成！处理了 {len(lines)} 行，输出 {len(cleaned_lines)} 行")
        print(f"结果保存到: {output_file}")
        
    except FileNotFoundError:
        print(f"错误：找不到文件 {input_file}")
    except Exception as e:
        print(f"错误：{e}")

def clean_text(text):
    """
    清理文本中的URL
    
    Args:
        text (str): 包含URL的文本
        
    Returns:
        str: 清理后的文本
    """
    lines = text.split('\n')
    cleaned_lines = []
    
    for line in lines:
        cleaned_line = clean_url_line(line)
        if cleaned_line:
            cleaned_lines.append(cleaned_line)
    
    return '\n'.join(cleaned_lines)

class URLCleaner(QMainWindow):
    def __init__(self):
        super().__init__()
        self.initUI()
        
    def initUI(self):
        self.setWindowTitle('URL Cleaner')
        self.setGeometry(100, 100, 800, 600)
        
        # Create central widget and layout
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # Create text edit for displaying content
        self.text_edit = QTextEdit()
        layout.addWidget(self.text_edit)
        
        # Create buttons
        load_button = QPushButton('Load File')
        load_button.clicked.connect(self.load_file)
        layout.addWidget(load_button)
        
        clean_button = QPushButton('Clean URLs')
        clean_button.clicked.connect(self.clean_urls)
        layout.addWidget(clean_button)
        
        save_button = QPushButton('Save File')
        save_button.clicked.connect(self.save_file)
        layout.addWidget(save_button)
        
    def load_file(self):
        file_name, _ = QFileDialog.getOpenFileName(self, 'Open File', '', 'Text Files (*.txt);;All Files (*)')
        if file_name:
            try:
                with open(file_name, 'r', encoding='utf-8') as file:
                    content = file.read()
                    self.text_edit.setText(content)
            except Exception as e:
                QMessageBox.critical(self, 'Error', f'Error loading file: {str(e)}')
    
    def clean_urls(self):
        content = self.text_edit.toPlainText()
        cleaned_lines = []
        
        for line in content.split('\n'):
            if ':' in line:
                # Find the first colon and remove everything before it
                parts = line.split(':', 1)
                if len(parts) > 1:
                    cleaned_lines.append(parts[1])
            else:
                cleaned_lines.append(line)
        
        cleaned_content = '\n'.join(cleaned_lines)
        self.text_edit.setText(cleaned_content)
        QMessageBox.information(self, 'Success', 'URLs have been cleaned!')
    
    def save_file(self):
        file_name, _ = QFileDialog.getSaveFileName(self, 'Save File', '', 'Text Files (*.txt);;All Files (*)')
        if file_name:
            try:
                with open(file_name, 'w', encoding='utf-8') as file:
                    content = self.text_edit.toPlainText()
                    file.write(content)
                QMessageBox.information(self, 'Success', 'File saved successfully!')
            except Exception as e:
                QMessageBox.critical(self, 'Error', f'Error saving file: {str(e)}')

def main():
    app = QApplication(sys.argv)
    window = URLCleaner()
    window.show()
    sys.exit(app.exec())

if __name__ == '__main__':
    main() 