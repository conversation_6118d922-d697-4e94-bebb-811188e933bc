with open('usf.txt', 'r') as infile, open('outlook.txt', 'w') as outfile:
    for line_num, line in enumerate(infile, 1):
        line = line.strip()
        # 如果行中没有冒号，将所有非冒号字符替换为冒号
        if ':' not in line:
            line = line.replace('，', ':').replace('：', ':').replace(';', ':').replace(' ', ':')
        
        # 检查处理后的行是否包含冒号
        if ':' in line:
            xxxxxx, password = line.split(':', 1)  # 只分割一次，防止密码中包含冒号
            new_line = f"{xxxxxx}@usf.edu:{password}\n"
            outfile.write(new_line)
        else:
            print(f"跳过无效行：第 {line_num} 行: {line}")

print("处理完成，结果已保存到outlook.txt文件中。")
