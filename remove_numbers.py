#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
删除msn.com邮箱前面数字的脚本
处理格式: "数字: 邮箱地址 密码" -> "邮箱地址 密码"
"""

import re
import os

def remove_numbers_from_emails(input_file='msn.txt', output_file='msn_cleaned.txt'):
    """
    从输入文件中删除邮箱前面的数字
    
    Args:
        input_file (str): 输入文件名，默认为'msn.txt'
        output_file (str): 输出文件名，默认为'msn_cleaned.txt'
    """
    
    # 检查输入文件是否存在
    if not os.path.exists(input_file):
        print(f"错误：文件 {input_file} 不存在！")
        return
    
    cleaned_lines = []
    total_lines = 0
    processed_lines = 0
    
    try:
        with open(input_file, 'r', encoding='utf-8', errors='ignore') as file:
            for line_num, line in enumerate(file, 1):
                total_lines += 1
                original_line = line.strip()
                
                # 跳过空行
                if not original_line:
                    cleaned_lines.append('')
                    continue
                
                # 使用正则表达式匹配并删除前面的数字和冒号
                # 匹配格式: "数字: 邮箱地址 密码"
                pattern = r'^\d+:\s*(.+)$'
                match = re.match(pattern, original_line)
                
                if match:
                    # 提取邮箱和密码部分（去掉前面的数字和冒号）
                    email_password_part = match.group(1).strip()
                    cleaned_lines.append(email_password_part)
                    processed_lines += 1
                    
                    # 显示处理进度（每100行显示一次）
                    if processed_lines % 100 == 0:
                        print(f"已处理 {processed_lines} 行...")
                else:
                    # 如果不匹配格式，保持原样
                    cleaned_lines.append(original_line)
        
        # 保存清理后的结果到输出文件
        with open(output_file, 'w', encoding='utf-8') as output:
            for line in cleaned_lines:
                output.write(line + '\n')
        
        print(f"\n处理完成！")
        print(f"总共处理了 {total_lines} 行")
        print(f"成功清理了 {processed_lines} 行的数字前缀")
        print(f"结果已保存到 {output_file}")
        
        # 显示前10行作为预览
        print(f"\n前10行处理结果预览:")
        for i, line in enumerate(cleaned_lines[:10], 1):
            if line.strip():  # 只显示非空行
                print(f"{i:2d}. {line}")
    
    except Exception as e:
        print(f"处理文件时出错: {e}")

def preview_changes(input_file='msn.txt', num_lines=10):
    """
    预览将要进行的更改
    
    Args:
        input_file (str): 输入文件名
        num_lines (int): 预览的行数
    """
    
    if not os.path.exists(input_file):
        print(f"错误：文件 {input_file} 不存在！")
        return
    
    print("=" * 80)
    print("更改预览 (前10行):")
    print("=" * 80)
    
    try:
        with open(input_file, 'r', encoding='utf-8', errors='ignore') as file:
            for line_num, line in enumerate(file, 1):
                if line_num > num_lines:
                    break
                
                original_line = line.strip()
                if not original_line:
                    continue
                
                # 应用相同的处理逻辑
                pattern = r'^\d+:\s*(.+)$'
                match = re.match(pattern, original_line)
                
                if match:
                    cleaned_line = match.group(1).strip()
                    print(f"原始: {original_line}")
                    print(f"清理: {cleaned_line}")
                    print("-" * 40)
                else:
                    print(f"保持: {original_line}")
                    print("-" * 40)
    
    except Exception as e:
        print(f"预览时出错: {e}")

def main():
    """主函数"""
    print("=" * 60)
    print("MSN邮箱数字前缀清理工具")
    print("=" * 60)

    # 先显示预览
    preview_changes()

    print("\n开始处理整个文件...")
    # 直接执行清理
    remove_numbers_from_emails()

    print("\n程序执行完毕！")

if __name__ == "__main__":
    main()
