import sys
import os
import time
import random
import smtplib
import string
import base64
import concurrent.futures
from email.mime.multipart import MI<PERSON><PERSON>ultipart
from email.mime.text import MIMEText
from platform import system
from time import strftime
from random import choice

from PyQt6.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
                            QLabel, QLineEdit, QPushButton, QTextEdit, QFileDialog, 
                            QSpinBox, QProgressBar, QGroupBox, QFormLayout, QMessageBox)
from PyQt6.QtCore import Qt, QThread, pyqtSignal, QTimer
from PyQt6.QtGui import QColor, QTextCursor, QFont, QIcon

class SMTPChecker(QThread):
    """SMTP检查的线程类"""
    update_log = pyqtSignal(str, str)  # 信号：文本和类型(success/error/info)
    update_progress = pyqtSignal(int)
    finished = pyqtSignal(list, list)  # 结束信号，传递好的和坏的SMTP列表

    def __init__(self, smtp_list, toaddr, thread_count):
        super().__init__()
        self.smtp_list = smtp_list
        self.toaddr = toaddr
        self.thread_count = thread_count
        self.good = []
        self.bad = []
        self.total = len(smtp_list)
        self.processed = 0

    def check_smtp(self, smtp):
        """检查单个SMTP服务器"""
        try:
            HOST, PORT, usr, pas = smtp.strip().split('|')
            
            # 尝试连接SMTP服务器
            self.update_log.emit(f"正在检查: {smtp}", "info")
            server = smtplib.SMTP(HOST, PORT)
            server.ehlo()
            server.starttls()
            server.login(usr, pas)
            
            # 创建邮件
            msg = MIMEMultipart()
            msg['Subject'] = 'SMTP检查结果'
            msg['From'] = usr
            msg['To'] = self.toaddr
            msg.add_header('Content-Type', 'text/html')
            
            # 邮件HTML内容
            data = f'''
            <!DOCTYPE html>
            <html lang="zh">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>SMTP 可用</title>
                <style>
                    .container {{
                        background-color: #f8f9fa;
                        padding: 20px;
                        border-radius: 10px;
                        font-family: Arial, sans-serif;
                    }}
                    .header {{
                        background-color: #4a86e8;
                        color: white;
                        padding: 10px;
                        text-align: center;
                        border-radius: 5px 5px 0 0;
                        font-size: 22px;
                    }}
                    .content {{
                        margin-top: 10px;
                        background-color: white;
                        padding: 15px;
                        border-radius: 0 0 5px 5px;
                        border: 1px solid #e0e0e0;
                    }}
                    .item {{
                        margin: 10px 0;
                        padding: 5px;
                        border-bottom: 1px solid #f0f0f0;
                    }}
                    .item-label {{
                        font-weight: bold;
                        color: #333;
                    }}
                    .item-value {{
                        color: #0066cc;
                    }}
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="header">
                        SMTP检查结果
                    </div>
                    <div class="content">
                        <div class="item">
                            <span class="item-label">主机:</span>
                            <span class="item-value">{HOST}</span>
                        </div>
                        <div class="item">
                            <span class="item-label">端口:</span>
                            <span class="item-value">{PORT}</span>
                        </div>
                        <div class="item">
                            <span class="item-label">用户名:</span>
                            <span class="item-value">{usr}</span>
                        </div>
                        <div class="item">
                            <span class="item-label">密码:</span>
                            <span class="item-value">{pas}</span>
                        </div>
                        <div class="item">
                            <span class="item-label">检查时间:</span>
                            <span class="item-value">{time.strftime('%Y-%m-%d %H:%M:%S')}</span>
                        </div>
                    </div>
                </div>
            </body>
            </html>
            '''
            
            msg.attach(MIMEText(data, 'html', 'utf-8'))
            
            # 发送邮件
            server.sendmail(usr, [msg['To']], msg.as_string())
            server.quit()
            
            # 处理成功结果
            self.good.append(smtp)
            self.update_log.emit(f"✅ SMTP可用: {smtp}", "success")
            
            # 保存有效的SMTP到文件
            with open(os.path.join('Result', 'valid.txt'), 'a+') as f:
                f.write(f"{smtp}\n")
                
            return True
        except Exception as e:
            # 处理失败结果
            self.bad.append(smtp)
            self.update_log.emit(f"❌ SMTP不可用: {smtp} (错误: {str(e)})", "error")
            
            # 保存无效的SMTP到文件
            with open(os.path.join('Result', 'invalid.txt'), 'a+') as f:
                f.write(f"{smtp}\n")
                
            return False
        finally:
            # 更新进度
            self.processed += 1
            progress = int((self.processed / self.total) * 100)
            self.update_progress.emit(progress)

    def run(self):
        """运行线程池进行SMTP检查"""
        try:
            # 确保Result目录存在
            os.makedirs('Result', exist_ok=True)
            
            with concurrent.futures.ThreadPoolExecutor(max_workers=self.thread_count) as executor:
                results = list(executor.map(self.check_smtp, self.smtp_list))
            
            # 发送完成信号
            self.finished.emit(self.good, self.bad)
        except Exception as e:
            self.update_log.emit(f"发生错误: {str(e)}", "error")


class SMTPCheckerApp(QMainWindow):
    """SMTP检查器应用主窗口"""
    def __init__(self):
        super().__init__()
        self.setWindowTitle("SMTP检查工具")
        self.setMinimumSize(800, 600)
        
        # 实例变量
        self.smtp_list = []
        self.checker_thread = None
        
        # 设置UI
        self.setup_ui()
        
        # 启动欢迎动画
        self.welcome_animation()

    def setup_ui(self):
        """设置UI界面"""
        # 主布局
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QVBoxLayout(central_widget)
        
        # 标题
        title_label = QLabel("SMTP检查工具")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        font = QFont("Arial", 18, QFont.Weight.Bold)
        title_label.setFont(font)
        title_label.setStyleSheet("color: #4a86e8; margin: 10px;")
        main_layout.addWidget(title_label)
        
        # 功能描述
        description = QLabel("检查SMTP服务器的有效性并将结果发送到指定邮箱")
        description.setAlignment(Qt.AlignmentFlag.AlignCenter)
        description.setStyleSheet("color: #666; margin-bottom: 15px;")
        main_layout.addWidget(description)
        
        # 输入部分
        input_group = QGroupBox("配置")
        input_layout = QFormLayout()
        input_group.setLayout(input_layout)
        
        # SMTP文件选择
        smtp_layout = QHBoxLayout()
        self.smtp_file_path = QLineEdit()
        self.smtp_file_path.setPlaceholderText("选择SMTP列表文件")
        smtp_layout.addWidget(self.smtp_file_path)
        
        browse_button = QPushButton("浏览")
        browse_button.clicked.connect(self.browse_smtp_file)
        smtp_layout.addWidget(browse_button)
        
        input_layout.addRow("SMTP列表:", smtp_layout)
        
        # 接收邮箱
        self.toaddr_input = QLineEdit()
        self.toaddr_input.setPlaceholderText("输入接收结果的邮箱地址")
        input_layout.addRow("接收邮箱:", self.toaddr_input)
        
        # 线程数
        self.thread_count = QSpinBox()
        self.thread_count.setRange(1, 50)
        self.thread_count.setValue(5)
        input_layout.addRow("线程数:", self.thread_count)
        
        main_layout.addWidget(input_group)
        
        # 操作按钮
        button_layout = QHBoxLayout()
        
        self.start_button = QPushButton("开始检查")
        self.start_button.clicked.connect(self.start_checking)
        self.start_button.setStyleSheet("background-color: #4CAF50; color: white; padding: 8px 16px;")
        button_layout.addWidget(self.start_button)
        
        self.stop_button = QPushButton("停止检查")
        self.stop_button.clicked.connect(self.stop_checking)
        self.stop_button.setStyleSheet("background-color: #f44336; color: white; padding: 8px 16px;")
        self.stop_button.setEnabled(False)
        button_layout.addWidget(self.stop_button)
        
        main_layout.addLayout(button_layout)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setTextVisible(True)
        self.progress_bar.setFormat("%p% (%v/%m)")
        self.progress_bar.setValue(0)
        main_layout.addWidget(self.progress_bar)
        
        # 日志输出
        log_group = QGroupBox("检查日志")
        log_layout = QVBoxLayout()
        log_group.setLayout(log_layout)
        
        self.log_output = QTextEdit()
        self.log_output.setReadOnly(True)
        log_layout.addWidget(self.log_output)
        
        main_layout.addWidget(log_group)
        
        # 状态栏
        self.statusBar().showMessage("就绪")

    def welcome_animation(self):
        """显示欢迎动画"""
        self.log_output.clear()
        
        # 创建欢迎文本
        welcome_text = '''
        SMTP检查工具
        
        功能:
        - 检查SMTP服务器的有效性
        - 自动发送有效SMTP到指定邮箱
        - 多线程并行处理提高效率
        - 保存结果到文件
        
        使用方法:
        1. 选择SMTP列表文件
        2. 输入接收结果的邮箱
        3. 设置线程数
        4. 点击"开始检查"
        
        准备就绪!
        '''
        
        # 逐行添加文本，模拟动画效果
        lines = welcome_text.strip().split('\n')
        
        def add_line(index):
            if index < len(lines):
                self.log_output.append(lines[index])
                QTimer.singleShot(100, lambda: add_line(index + 1))
        
        add_line(0)

    def browse_smtp_file(self):
        """浏览并选择SMTP列表文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择SMTP列表文件", "", "文本文件 (*.txt);;所有文件 (*.*)"
        )
        
        if file_path:
            self.smtp_file_path.setText(file_path)
            
            # 尝试读取文件
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    self.smtp_list = f.read().splitlines()
                self.log(f"已加载 {len(self.smtp_list)} 个SMTP地址", "info")
                
                # 更新进度条最大值
                self.progress_bar.setMaximum(len(self.smtp_list))
                self.progress_bar.setValue(0)
            except Exception as e:
                self.log(f"读取文件错误: {str(e)}", "error")

    def log(self, message, msg_type="info"):
        """向日志窗口添加消息"""
        timestamp = time.strftime("%H:%M:%S")
        
        # 根据消息类型设置不同颜色
        if msg_type == "success":
            color = "green"
        elif msg_type == "error":
            color = "red"
        elif msg_type == "info":
            color = "blue"
        else:
            color = "black"
        
        # 格式化HTML
        html = f'<span style="color:{color}">[{timestamp}] {message}</span>'
        
        # 添加到日志
        self.log_output.append(html)
        
        # 自动滚动到底部
        self.log_output.moveCursor(QTextCursor.MoveOperation.End)

    def start_checking(self):
        """开始SMTP检查"""
        # 验证输入
        smtp_file = self.smtp_file_path.text()
        toaddr = self.toaddr_input.text()
        thread_count = self.thread_count.value()
        
        if not smtp_file:
            QMessageBox.warning(self, "输入错误", "请选择SMTP列表文件")
            return
            
        if not toaddr:
            QMessageBox.warning(self, "输入错误", "请输入接收邮箱地址")
            return
            
        if not self.smtp_list:
            QMessageBox.warning(self, "输入错误", "SMTP列表为空")
            return
        
        # 更新UI状态
        self.start_button.setEnabled(False)
        self.stop_button.setEnabled(True)
        self.progress_bar.setValue(0)
        self.progress_bar.setMaximum(len(self.smtp_list))
        self.log_output.clear()
        self.log("开始检查SMTP...", "info")
        
        # 创建并启动检查线程
        self.checker_thread = SMTPChecker(self.smtp_list, toaddr, thread_count)
        self.checker_thread.update_log.connect(self.log)
        self.checker_thread.update_progress.connect(self.progress_bar.setValue)
        self.checker_thread.finished.connect(self.on_check_finished)
        self.checker_thread.start()
        
        # 更新状态栏
        self.statusBar().showMessage("检查中...")

    def stop_checking(self):
        """停止SMTP检查"""
        if self.checker_thread and self.checker_thread.isRunning():
            self.checker_thread.terminate()
            self.log("检查已手动停止", "info")
            self.statusBar().showMessage("已停止")
            
            # 更新UI状态
            self.start_button.setEnabled(True)
            self.stop_button.setEnabled(False)

    def on_check_finished(self, good, bad):
        """检查完成的回调"""
        # 更新UI状态
        self.start_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        
        # 显示结果
        self.log(f"检查完成! 有效: {len(good)}, 无效: {len(bad)}", "success")
        self.statusBar().showMessage(f"就绪 - 上次检查: 有效 {len(good)}, 无效 {len(bad)}")
        
        # 显示完成对话框
        QMessageBox.information(self, "检查完成", 
                               f"SMTP检查已完成\n\n有效: {len(good)}\n无效: {len(bad)}")


if __name__ == "__main__":
    # 确保Result目录存在
    os.makedirs('Result', exist_ok=True)
    
    # 创建应用
    app = QApplication(sys.argv)
    app.setStyle('Fusion')  # 使用Fusion风格
    
    # 设置应用样式
    app.setStyleSheet("""
        QMainWindow {
            background-color: #f5f5f5;
        }
        QGroupBox {
            border: 1px solid #ddd;
            border-radius: 5px;
            margin-top: 1ex;
            padding: 10px;
            background-color: white;
        }
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 3px 0 3px;
            color: #4a86e8;
        }
        QPushButton {
            border-radius: 4px;
            padding: 6px;
        }
        QTextEdit, QLineEdit {
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 5px;
        }
        QProgressBar {
            border: 1px solid #ddd;
            border-radius: 4px;
            text-align: center;
            height: 20px;
        }
        QProgressBar::chunk {
            background-color: #4a86e8;
            width: 10px;
        }
    """)
    
    # 创建窗口
    window = SMTPCheckerApp()
    window.show()
    
    # 运行应用
    sys.exit(app.exec()) 