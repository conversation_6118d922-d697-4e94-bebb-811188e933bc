#!/usr/bin/env python
# -*- coding: utf-8 -*-

def extract_links():
    """
    从us.txt文件中提取包含cas.columbia.edu/cas/login的链接，
    并将这些链接保存到columbia.txt文件中
    """
    try:
        # 打开输入文件和输出文件
        with open('us.txt', 'r', encoding='utf-8') as input_file, \
             open('columbia.txt', 'w', encoding='utf-8') as output_file:
            
            # 逐行读取输入文件
            for line in input_file:
                # 检查该行是否包含指定文本
                if 'cas.columbia.edu/cas/login' in line:
                    # 将匹配的行写入输出文件
                    output_file.write(line)
        
        print("提取完成！匹配的链接已保存到 columbia.txt 文件中。")
    except Exception as e:
        print(f"发生错误: {e}")

if __name__ == "__main__":
    extract_links()
